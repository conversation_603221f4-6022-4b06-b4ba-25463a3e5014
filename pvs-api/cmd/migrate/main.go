package main

import (
	"flag"
	"fmt"
	"os"
	"pvs-api/cmd/migrate/migrations"
	"pvs-api/internal/config"
	"pvs-api/internal/db"
)

func main() {
	// Define command-line flags
	autoCmd := flag.Bool("auto", false, "Auto migration")
	seedCmd := flag.Bool("seed", false, "Init seed data")
	cleanupCmd := flag.Bool("cleanup", false, "Clean up database (drop all tables and sequences)")
	forceCleanup := flag.Bool("force", false, "Force cleanup without confirmation (use with -cleanup)")

	// Parse flags
	flag.Parse()

	// Validate commands
	if !(*autoCmd || *seedCmd || *cleanupCmd) {
		fmt.Println("Error: No command specified")
		printUsage()
		os.Exit(1)
	}

	// Load configuration
	cfg := config.LoadConfig()

	// Connect to the database without running migrations
	database, err := db.SetupDB(&cfg.Database)
	if err != nil {
		fmt.Printf("Error connecting to database: %v\n", err)
		os.Exit(1)
	}

	// Execute the requested command
	if *cleanupCmd {
		// Confirm cleanup unless force flag is set
		if !*forceCleanup {
			fmt.Println("WARNING: This will delete ALL tables and data in the database.")
			fmt.Print("Are you sure you want to continue? (y/N): ")

			var response string
			fmt.Scanln(&response)

			if response != "y" && response != "Y" {
				fmt.Println("Cleanup aborted.")
				os.Exit(0)
			}
		}

		// Proceed with cleanup
		if err := migrations.CleanupDatabase(database); err != nil {
			fmt.Printf("Error cleaning up database: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Database cleanup completed successfully")
	} else if *autoCmd {
		if err := migrations.RunMigrations(database); err != nil {
			fmt.Printf("Error running migrations: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Migrations completed successfully")
	} else if *seedCmd {
		if err := migrations.SeedAll(database); err != nil {
			fmt.Printf("Error seeding data: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("Seed data completed successfully")
	}
}

// printUsage prints usage information
func printUsage() {
	fmt.Println("Usage:")
	fmt.Println("  migrate -auto                          Run auto migrations")
	fmt.Println("  migrate -seed                          Create seed data")
	fmt.Println("  migrate -cleanup                       Clean up database (drop all tables and sequences)")
	fmt.Println("  migrate -cleanup -force                Clean up database without confirmation")
}
