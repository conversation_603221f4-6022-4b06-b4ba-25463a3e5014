package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedRoles tạo dữ liệu ban đầu cho bảng Role
func SeedRoles(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.Role{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("Role table already has data, skipping seed")
		return nil
	}

	// Tạo roles mặc định
	roles := []*models.Role{
		{
			Code:        "ADMIN",
			Name:        "Administrator",
			Description: "Quản trị viên hệ thống",
			IsSystem:    true,
			Permissions: models.AllPermissions(), // Admin có tất cả permissions
		},
		{
			Code:        "PRODUCT_MANAGER",
			Name:        "Product Manager",
			Description: "Quản lý sản phẩm",
			IsSystem:    true,
			Permissions: models.GetPermissionsByGroup("product"),
		},
		{
			Code:        "COMPONENT_MANAGER",
			Name:        "Component Manager",
			Description: "Quản lý thành phần",
			IsSystem:    true,
			Permissions: models.GetPermissionsByGroup("component"),
		},
		{
			Code:        "USER_MANAGER",
			Name:        "User Manager",
			Description: "Quản lý người dùng",
			IsSystem:    true,
			Permissions: models.GetPermissionsByGroup("user"),
		},
		{
			Code:        "VIEWER",
			Name:        "Viewer",
			Description: "Người xem",
			IsSystem:    true,
			Permissions: models.GetViewPermissions(),
		},
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, role := range roles {
			if err := tx.Create(role).Error; err != nil {
				return fmt.Errorf("failed to create role %s: %w", role.Code, err)
			}
		}
		fmt.Println("Role data seeded successfully")
		return nil
	})
}
