package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedNotableSystems tạo dữ liệu ban đầu cho bảng NotableSystem
func SeedNotableSystems(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.NotableSystem{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("NotableSystem table already has data, skipping seed")
		return nil
	}

	// Tạo notable systems mặc định
	notableSystems := []*models.NotableSystem{
		{
			Name:        "Core System",
			Description: "Hệ thống lõi Chứng khoán",
			StartYear:   2020,
			EndYear:     nil,
		},
	}

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &notableSystems, "NotableSystem")
}
