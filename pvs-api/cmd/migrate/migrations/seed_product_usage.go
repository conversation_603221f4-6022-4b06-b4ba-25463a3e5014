package migrations

import (
	"context"
	"fmt"
	"math/rand"
	"pvs-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SeedProductUsage tạo dữ liệu mẫu cho bảng ProductUsage
func SeedProductUsage(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.ProductUsageStat{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("ProductUsageStat table already has data, skipping seed")
		return nil
	}

	// Lấy danh sách tất cả products
	var products []models.Product
	if err := db.Find(&products).Error; err != nil {
		return fmt.Errorf("failed to fetch products: %w", err)
	}

	if len(products) == 0 {
		return fmt.Errorf("no products found to generate usage stats")
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Tạo dữ liệu cho 12 tháng gần nhất cho mỗi sản phẩm
		currentTime := time.Now()
		startMonth := time.Date(currentTime.Year()-1, currentTime.Month()+1, 1, 0, 0, 0, 0, time.UTC)

		for _, product := range products {
			// Tạo baseline số người dùng cho sản phẩm này (100-1000)
			baseUsers := rand.Intn(901) + 100
			monthlyGrowth := rand.Float64()*0.1 + 0.02 // 2-12% tăng trưởng mỗi tháng

			currentUsers := float64(baseUsers)
			currentMonth := startMonth

			for i := 0; i < 12; i++ {
				// Thêm một chút nhiễu ngẫu nhiên (-5% đến +5%)
				noise := 1.0 + (rand.Float64()*0.1 - 0.05)

				usageStat := models.ProductUsageStat{
					ProductID: product.ID,
					Month:     currentMonth,
					Users:     int(currentUsers * noise),
				}

				if err := tx.Create(&usageStat).Error; err != nil {
					return fmt.Errorf("failed to create usage stat for product %d: %w", product.ID, err)
				}

				// Tăng số người dùng cho tháng tiếp theo
				currentUsers *= (1.0 + monthlyGrowth)
				currentMonth = currentMonth.AddDate(0, 1, 0)
			}
		}

		fmt.Println("ProductUsageStat data seeded successfully")
		return nil
	})
}
