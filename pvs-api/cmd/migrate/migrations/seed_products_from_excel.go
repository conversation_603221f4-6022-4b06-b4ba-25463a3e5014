package migrations

import (
	"fmt"
	"pvs-api/internal/models"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// Mapping cho các giá trị StageType
var stageMapping = map[string]string{
	"Golive tới khách hàng": "GOLIVE",
	"Concept sản phẩm":      "IDEA",
	"Golive nội bộ":         "PILOT",
	"Phát triển hệ thống":   "DEVELOP_AND_TEST",
	"Đánh giá tiền khả thi": "PRE_FEASIBILITY",
	"Nghiên cứu":            "MARKET_RESEARCH",
}

func parseFlexibleDate(dateStr string) (time.Time, error) {
	// Define possible formats to try
	formats := []string{
		"01/02/06",   // mm-dd-yy
		"01/02/2006", // mm-dd-yyyy
		"1/2/2006",   // mm-dd-yyyy
		// Add more formats as needed
	}

	var lastErr error
	for _, format := range formats {
		date, err := time.Parse(format, dateStr)
		if err == nil {
			return date, nil
		}
		lastErr = err
	}

	return time.Time{}, fmt.Errorf("failed to parse date '%s': %v", dateStr, lastErr)
}

// SeedProductsFromExcel đọc dữ liệu từ file Excel và tạo các Products và related data
func SeedProductsFromExcel(db *gorm.DB) error {
	// Kiểm tra dữ liệu hiện có trong bảng Product thay vì ProductGroup
	hasData, err := CheckTableHasData(db, &models.Product{})
	if err != nil {
		return err
	}

	if hasData {
		fmt.Println("Product table already has data, skipping seed")
		return nil
	}

	// Đọc file Excel
	f, err := excelize.OpenFile("data/products.xlsx")
	if err != nil {
		return fmt.Errorf("failed to open Excel file: %w", err)
	}
	defer f.Close()

	// Lấy sheet đầu tiên
	sheetName := f.GetSheetList()[0]

	// Lấy tất cả các rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("failed to get rows: %w", err)
	}

	// Bỏ qua hàng header
	if len(rows) <= 1 {
		return fmt.Errorf("excel file is empty or contains only headers")
	}

	// Map để lưu trữ ProductGroup theo tên
	productGroups := make(map[string]*models.ProductGroup)
	var currentGroupName string

	return db.Transaction(func(tx *gorm.DB) error {
		// Xử lý từng row, bắt đầu từ row thứ 2 (index 1)
		for _, row := range rows[1:] {
			if len(row) == 1 {
				// Xử lý ProductGroup
				groupName := strings.TrimSpace(row[0])
				if groupName != "" {
					currentGroupName = groupName
					if _, exists := productGroups[currentGroupName]; !exists {
						productGroup := &models.ProductGroup{
							Name: currentGroupName,
						}
						if err := tx.Create(productGroup).Error; err != nil {
							return fmt.Errorf("failed to create product group: %w", err)
						}
						productGroups[currentGroupName] = productGroup
					}
					continue // Nếu là dòng group thì skip các xử lý tiếp theo
				}
			}

			// Xử lý Product
			productName := strings.TrimSpace(row[1])
			if productName == "" {
				continue
			}

			description := strings.TrimSpace(row[2])
			stageString := strings.TrimSpace(row[3])

			// Parse các ngày
			startDateStr := strings.TrimSpace(row[5])
			startDate, err := parseFlexibleDate(startDateStr)
			if err != nil {
				return fmt.Errorf("failed to parse start date '%s': %w", startDateStr, err)
			}

			var idleDate, endDate *time.Time

			if len(row) > 6 {
				if idleDateStr := strings.TrimSpace(row[6]); idleDateStr != "" {
					parsedDate, err := parseFlexibleDate(idleDateStr)
					if err != nil {
						return fmt.Errorf("failed to parse idle date '%s': %w", idleDateStr, err)
					}
					idleDate = &parsedDate
				}
			}

			if len(row) > 7 {
				if endDateStr := strings.TrimSpace(row[7]); endDateStr != "" {
					parsedDate, err := parseFlexibleDate(endDateStr)
					if err != nil {
						return fmt.Errorf("failed to parse end date '%s': %w", endDateStr, err)
					}
					endDate = &parsedDate
				}
			}

			// Xác định status hiện tại dựa trên các ngày
			currentStatus := "ACTIVE"
			if endDate != nil {
				currentStatus = "INACTIVE"
			} else if idleDate != nil {
				currentStatus = "IDLE"
			}

			// Tạo Product trước (without ProductGroups to avoid duplicate associations)
			product := &models.Product{
				Name:        productName,
				Description: description,
				Status:      currentStatus,
				StageID:     nil, // StageID is a pointer to uint, so nil is valid
			}

			if err := tx.Create(product).Error; err != nil {
				return fmt.Errorf("failed to create product: %w", err)
			}

			// Explicitly create the association with ProductGroup
			if currentGroupName != "" {
				// The unique index will prevent duplicates, so we can just try to create the association
				if err := tx.Model(product).Association("ProductGroups").Append(productGroups[currentGroupName]); err != nil {
					// If there's a duplicate key error, we can ignore it
					if !strings.Contains(err.Error(), "duplicate key") {
						return fmt.Errorf("failed to associate product with product group: %w", err)
					}
				}
			}

			// Sau đó tạo ProductStage và liên kết với Product
			stage := &models.ProductStage{
				ProductID: product.ID,
				StageCode: stageMapping[stageString],
				StartDate: startDate,
				EndDate:   endDate,
			}

			if err := tx.Create(stage).Error; err != nil {
				return fmt.Errorf("failed to create product stage: %w", err)
			}

			// Cập nhật StageID cho Product
			stageID := stage.ID // Create a copy of stage.ID
			product.StageID = &stageID

			// Update the product in the database
			if err := tx.Model(product).Update("stage_id", stage.ID).Error; err != nil {
				return fmt.Errorf("failed to update product's stage_id: %w", err)
			}

			statusLog := &models.ProductStatusLog{
				ProductID: product.ID,
				Status:    "ACTIVE",
				ChangedBy: "system",
				ChangedAt: startDate,
				Note:      "Initial product status",
			}

			if err := tx.Create(statusLog).Error; err != nil {
				return fmt.Errorf("failed to create initial status log: %w", err)
			}

			// Tạo ProductStatusLog cho trạng thái duy trì (nếu có)
			if idleDate != nil {
				idleLog := &models.ProductStatusLog{
					ProductID: product.ID,
					Status:    "IDLE",
					ChangedBy: "system",
					ChangedAt: *idleDate,
					Note:      "Product moved to idle",
				}
				if err := tx.Create(idleLog).Error; err != nil {
					return fmt.Errorf("failed to create idle status log: %w", err)
				}
			}

			// Tạo ProductStatusLog cho trạng thái kết thúc (nếu có)
			if endDate != nil {
				endLog := &models.ProductStatusLog{
					ProductID: product.ID,
					Status:    "INACTIVE",
					ChangedBy: "system",
					ChangedAt: *endDate,
					Note:      "Product deactivated",
				}
				if err := tx.Create(endLog).Error; err != nil {
					return fmt.Errorf("failed to create end status log: %w", err)
				}
			}

			// Xử lý Components
			if len(row) > 8 {
				if components := strings.TrimSpace(row[8]); components != "" {
					componentNames := strings.Split(components, ",")
					for _, name := range componentNames {
						name = strings.TrimSpace(name)
						var component models.Component
						if err := tx.Where("name = ?", name).First(&component).Error; err != nil {
							continue
						}

						// The unique index will prevent duplicates, so we can just try to create the association
						if err := tx.Model(product).Association("Components").Append(&component); err != nil {
							// If there's a duplicate key error, we can ignore it
							if !strings.Contains(err.Error(), "duplicate key") {
								return fmt.Errorf("failed to associate component: %w", err)
							}
						}
					}
				}
			}
		}

		fmt.Println("Products data seeded successfully")
		return nil
	})
}
