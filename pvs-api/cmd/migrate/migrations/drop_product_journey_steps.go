package migrations

import (
	"fmt"
	"gorm.io/gorm"
)

// DropProductJourneyStepsTable drops the product_journey_steps table if it exists
func DropProductJourneyStepsTable(db *gorm.DB) error {
	fmt.Println("Dropping product_journey_steps table if it exists...")
	
	// Check if table exists and drop it
	if db.Migrator().HasTable("product_journey_steps") {
		err := db.Migrator().DropTable("product_journey_steps")
		if err != nil {
			return fmt.Errorf("failed to drop product_journey_steps table: %w", err)
		}
		fmt.Println("Successfully dropped product_journey_steps table")
	} else {
		fmt.Println("product_journey_steps table does not exist, skipping...")
	}
	
	return nil
}
