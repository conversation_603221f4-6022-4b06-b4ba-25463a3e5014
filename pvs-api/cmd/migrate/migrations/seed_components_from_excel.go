package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"
	"strings"

	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

// SeedComponentsFromExcel đọc dữ liệu từ file Excel và tạo các Components
func SeedComponentsFromExcel(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra dữ liệu hiện có
	hasData, err := CheckTableHasData(db, &models.Component{})
	if err != nil {
		return err
	}

	if hasData {
		fmt.Println("Component table already has data, skipping seed")
		return nil
	}

	// Đọc file Excel
	f, err := excelize.OpenFile("data/components.xlsx")
	if err != nil {
		return fmt.Errorf("failed to open Excel file: %w", err)
	}
	defer f.Close()

	// Lấy sheet đầu tiên
	sheetName := f.GetSheetList()[0]

	// <PERSON><PERSON><PERSON> tất cả các rows
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("failed to get rows: %w", err)
	}

	// Bỏ qua hàng header
	if len(rows) <= 1 {
		return fmt.Errorf("excel file is empty or contains only headers")
	}

	components := make([]*models.Component, 0, len(rows)-1)

	// Xử lý từng row, bắt đầu từ row thứ 2 (index 1)
	for rowIndex, row := range rows[1:] {
		if len(row) < 8 { // Kiểm tra đủ số cột
			continue
		}

		// Lấy cell coordinate cho cột Name (cột B)
		cellCoord := fmt.Sprintf("B%d", rowIndex+2) // +2 vì bắt đầu từ row 2

		// Lấy hyperlink từ cell
		var confluenceURL string
		if is_link, hyperlink, err := f.GetCellHyperLink(sheetName, cellCoord); is_link && err == nil {
			confluenceURL = hyperlink
		}

		// Chuẩn hóa ComponentType và TargetUser
		componentType := normalizeCode(row[3]) // ComponentType ở cột thứ 4
		targetUser := normalizeCode(row[4])    // TargetUser ở cột thứ 5

		component := &models.Component{
			Name:              strings.TrimSpace(row[1]),
			Description:       strings.TrimSpace(row[2]),
			ComponentTypeCode: componentType,
			TargetUserCode:    targetUser,
			Importance:        strings.TrimSpace(row[5]),
			MainFunction:      strings.TrimSpace(row[6]),
			ExtraFunction:     strings.TrimSpace(row[7]),
			ConfluenceURL:     confluenceURL,
		}

		components = append(components, component)
	}

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &components, "Component")
}

// normalizeCode chuẩn hóa mã code
func normalizeCode(input string) string {
	// Chuyển đổi thành chữ hoa và thay thế khoảng trắng bằng dấu gạch dưới
	code := strings.ToUpper(strings.TrimSpace(input))
	code = strings.ReplaceAll(code, " ", "_")
	return code
}
