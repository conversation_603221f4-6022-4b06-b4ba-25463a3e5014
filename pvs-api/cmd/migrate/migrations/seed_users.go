package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedUsers tạo dữ liệu ban đầu cho bảng User
func SeedUsers(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.User{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("User table already has data, skipping seed")
		return nil
	}

	// Tạo user admin mặc định
	adminUser := &models.User{
		Username: "admin",
		Password: "Admin@123", // Sẽ được hash tự động bởi BeforeSave hook
		FullName: "System Administrator",
		RoleCode: "ADMIN",
		IsActive: true,
	}

	// Tạo dữ liệu trong transaction
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}
		fmt.Println("Admin user created successfully")
		return nil
	})
}
