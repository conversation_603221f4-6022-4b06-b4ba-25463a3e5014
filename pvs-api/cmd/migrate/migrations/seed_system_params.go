package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// SeedSystemParams tạo dữ liệu ban đầu cho bảng SystemParam
func SeedSystemParams(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.SystemParam{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("SystemParam table already has data, skipping seed")
		return nil
	}

	// Tạo dữ liệu ban đầu cho ProductStatus
	productStatuses := []models.SystemParam{
		{
			ParamType:    models.ParamTypeProductStatus,
			Code:         "ACTIVE",
			Name:         "Đang hoạt động",
			DisplayOrder: 1,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeProductStatus,
			Code:         "LOW_PRIORITY",
			Name:         "Ưu tiên thấp",
			DisplayOrder: 2,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeProductStatus,
			Code:         "INACTIVE",
			Name:         "Không hoạt động",
			DisplayOrder: 3,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeProductStatus,
			Code:         "ARCHIVED",
			Name:         "Đã lưu trữ",
			DisplayOrder: 4,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeProductStatus,
			Code:         "DELETED",
			Name:         "Đã xóa",
			DisplayOrder: 5,
			IsActive:     true,
		},
	}

	// Tạo dữ liệu ban đầu cho StageType
	stageTypes := []models.SystemParam{
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "IDEA",
			Name:         "Giai đoạn ý tưởng",
			DisplayOrder: 1,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "MARKET_RESEARCH",
			Name:         "Nghiên cứu thị trường",
			DisplayOrder: 2,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "PRODUCT_VISION",
			Name:         "Tầm nhìn sản phẩm",
			DisplayOrder: 3,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "FEASIBILITY",
			Name:         "Tính khả thi",
			DisplayOrder: 4,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "BUILD_PRODUCT",
			Name:         "Xây dựng sản phẩm",
			DisplayOrder: 5,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "SYSTEM_REQUIREMENTS",
			Name:         "Yêu cầu hệ thống",
			DisplayOrder: 6,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "PRE_FEASIBILITY",
			Name:         "Đánh giá tiền khả thi",
			DisplayOrder: 7,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "DEVELOP_AND_TEST",
			Name:         "Phát triển và kiểm thử",
			DisplayOrder: 8,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "PILOT",
			Name:         "Giai đoạn thử nghiệm",
			DisplayOrder: 9,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStageType,
			Code:         "GOLIVE",
			Name:         "Giai đoạn chính thức",
			DisplayOrder: 10,
			IsActive:     true,
		},
	}

	// Tạo dữ liệu ban đầu cho ComponentType
	componentTypes := []models.SystemParam{
		{
			ParamType:    models.ParamTypeComponentType,
			Code:         "MOBILE",
			Name:         "Ứng dụng di động",
			DisplayOrder: 1,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeComponentType,
			Code:         "DESKTOP",
			Name:         "Ứng dụng máy tính",
			DisplayOrder: 2,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeComponentType,
			Code:         "WEBSITE",
			Name:         "Trang Web",
			DisplayOrder: 3,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeComponentType,
			Code:         "INTERNAL",
			Name:         "Nội bộ",
			DisplayOrder: 4,
			IsActive:     true,
		},
	}

	// Tạo dữ liệu ban đầu cho TargetUser
	targetUsers := []models.SystemParam{
		{
			ParamType:    models.ParamTypeTargetUser,
			Code:         "TVTC",
			Name:         "Tư vấn tài chính",
			DisplayOrder: 1,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeTargetUser,
			Code:         "TVDT",
			Name:         "Tư vấn đầu tư",
			DisplayOrder: 2,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeTargetUser,
			Code:         "CUSTOMER",
			Name:         "Khách hàng",
			DisplayOrder: 3,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeTargetUser,
			Code:         "IT",
			Name:         "Nhân viên IT",
			DisplayOrder: 4,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeTargetUser,
			Code:         "BACK",
			Name:         "Nhân sự Back",
			DisplayOrder: 5,
			IsActive:     true,
		},
	}

	// Tạo dữ liệu ban đầu cho StakeholderRole
	stakeholderRoles := []models.SystemParam{
		{
			ParamType:    models.ParamTypeStakeholderRole,
			Code:         "PRIMARY",
			Name:         "Chính",
			DisplayOrder: 1,
			IsActive:     true,
		},
		{
			ParamType:    models.ParamTypeStakeholderRole,
			Code:         "COLLABORATOR",
			Name:         "Cộng tác",
			DisplayOrder: 2,
			IsActive:     true,
		},
	}

	// Gộp tất cả dữ liệu
	allParams := append(productStatuses, stageTypes...)
	allParams = append(allParams, componentTypes...)
	allParams = append(allParams, targetUsers...)
	allParams = append(allParams, stakeholderRoles...)

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &allParams, "SystemParam")
}
