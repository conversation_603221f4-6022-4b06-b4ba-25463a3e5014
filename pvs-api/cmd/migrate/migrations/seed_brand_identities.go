package migrations

import (
	"context"
	"fmt"
	"pvs-api/internal/models"
	"time"

	"gorm.io/gorm"
)

// SeedBrandIdentities tạo dữ liệu ban đầu cho bảng BrandIdentity
func SeedBrandIdentities(db *gorm.DB) error {
	ctx := context.Background()

	// Kiểm tra xem đã có dữ liệu trong bảng chưa
	hasData, err := CheckTableHasData(db, &models.BrandIdentity{})
	if err != nil {
		return err
	}

	// Nếu đã có dữ liệu, không cần seed
	if hasData {
		fmt.Println("BrandIdentity table already has data, skipping seed")
		return nil
	}

	// Tạo brand identity mặc định
	brandIdentities := []*models.BrandIdentity{
		{
			CompanyName: "PVS",
			ColorCode:   "#000000",
			LogoURL:     "https://example.com/logo.png",
			EffectiveAt: time.Now(),
			Note:        "Initial brand identity",
		},
	}

	// Tạo dữ liệu trong transaction
	return CreateInTransaction(ctx, db, &brandIdentities, "BrandIdentity")
}
