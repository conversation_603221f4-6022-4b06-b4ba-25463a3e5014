package migrations

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// CleanupDatabase drops all tables and sequences in the database
func CleanupDatabase(db *gorm.DB) error {
	fmt.Println("Starting database cleanup...")

	// Create a new session
	session := db.Session(&gorm.Session{})

	// Disable foreign key checks to avoid dependency issues during cleanup
	session.Exec("SET session_replication_role = 'replica'")
	defer session.Exec("SET session_replication_role = 'origin'")

	// Step 1: Get a list of all tables in the public schema
	var tables []string
	if err := session.Raw("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").Scan(&tables).Error; err != nil {
		return fmt.Errorf("failed to get tables: %w", err)
	}

	if len(tables) == 0 {
		fmt.Println("No tables found to drop")
		return nil
	}

	// Step 2: Drop all tables
	fmt.Printf("Found %d tables to drop\n", len(tables))

	// Build a query to drop all tables at once
	dropTablesQuery := fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", strings.Join(tables, ", "))

	fmt.Println("Dropping all tables...")
	if err := session.Exec(dropTablesQuery).Error; err != nil {
		return fmt.Errorf("failed to drop tables: %w", err)
	}

	// Step 3: Get a list of all sequences
	var sequences []string
	if err := session.Raw("SELECT sequence_name FROM information_schema.sequences WHERE sequence_schema = 'public'").Scan(&sequences).Error; err != nil {
		return fmt.Errorf("failed to get sequences: %w", err)
	}

	if len(sequences) == 0 {
		fmt.Println("No sequences found to drop")
		return nil
	}

	// Step 4: Drop all sequences
	fmt.Printf("Found %d sequences to drop\n", len(sequences))

	// Build a query to drop all sequences at once
	dropSequencesQuery := fmt.Sprintf("DROP SEQUENCE IF EXISTS %s CASCADE", strings.Join(sequences, ", "))

	fmt.Println("Dropping all sequences...")
	if err := session.Exec(dropSequencesQuery).Error; err != nil {
		return fmt.Errorf("failed to drop sequences: %w", err)
	}

	fmt.Println("Database cleanup completed successfully")
	return nil
}
