package migrations

import (
	"context"
	"fmt"

	"gorm.io/gorm"
)

// CheckTableHasData kiểm tra xem bảng đã có dữ liệu chưa
func CheckTableHasData(db *gorm.DB, model interface{}) (bool, error) {
	var count int64
	result := db.Model(model).Count(&count)

	if result.Error != nil {
		return false, fmt.Errorf("failed to count records: %w", result.Error)
	}
	return count > 0, nil
}

// CreateInTransaction tạo nhiều bản ghi trong một transaction
func CreateInTransaction(ctx context.Context, db *gorm.DB, records interface{}, recordType string) error {
	return db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(records).Error; err != nil {
			return fmt.Errorf("failed to create %s: %w", recordType, err)
		}
		fmt.Printf("%s data seeded successfully\n", recordType)
		return nil
	})
}
