package migrations

import (
	"fmt"
	"gorm.io/gorm"
)

// MigrateProductJourneyComponents migrates from old ProductJourneyComponent model to many2many relationship
func MigrateProductJourneyComponents(db *gorm.DB) error {
	fmt.Println("Migrating ProductJourneyComponent to many2many relationship...")

	// Check if old table exists
	if !db.Migrator().HasTable("product_journey_components") {
		fmt.Println("Old product_journey_components table does not exist, skipping migration...")
		return nil
	}

	// Check if the old table has the old structure (with id, journey_id, component_id)
	hasOldStructure := db.Migrator().HasColumn("product_journey_components", "id") &&
		db.Migrator().HasColumn("product_journey_components", "journey_id")

	if !hasOldStructure {
		fmt.Println("Table already has new structure, skipping migration...")
		return nil
	}

	return db.Transaction(func(tx *gorm.DB) error {
		// Create temporary table with new structure
		fmt.Println("Creating temporary table with new structure...")
		if err := tx.Exec(`
			CREATE TABLE IF NOT EXISTS product_journey_components_new (
				product_journey_id BIGINT NOT NULL,
				component_id BIGINT NOT NULL,
				PRIMARY KEY (product_journey_id, component_id)
			)
		`).Error; err != nil {
			return fmt.Errorf("failed to create temporary table: %w", err)
		}

		// Migrate data from old structure to new structure
		fmt.Println("Migrating data to new structure...")
		if err := tx.Exec(`
			INSERT INTO product_journey_components_new (product_journey_id, component_id)
			SELECT journey_id, component_id 
			FROM product_journey_components
			WHERE journey_id IS NOT NULL AND component_id IS NOT NULL
			ON CONFLICT (product_journey_id, component_id) DO NOTHING
		`).Error; err != nil {
			return fmt.Errorf("failed to migrate data: %w", err)
		}

		// Drop old table
		fmt.Println("Dropping old table...")
		if err := tx.Exec("DROP TABLE product_journey_components").Error; err != nil {
			return fmt.Errorf("failed to drop old table: %w", err)
		}

		// Rename new table
		fmt.Println("Renaming new table...")
		if err := tx.Exec("ALTER TABLE product_journey_components_new RENAME TO product_journey_components").Error; err != nil {
			return fmt.Errorf("failed to rename table: %w", err)
		}

		fmt.Println("ProductJourneyComponent migration completed successfully")
		return nil
	})
}
