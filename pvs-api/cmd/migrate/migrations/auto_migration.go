package migrations

import (
	"fmt"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

// RunMigrations runs database migrations using GORM's AutoMigrate
func RunMigrations(db *gorm.DB) error {
	// STEP 0: Drop deprecated tables
	fmt.Println("STEP 0: Dropping deprecated tables...")
	if err := DropProductJourneyStepsTable(db); err != nil {
		return fmt.Errorf("failed to drop deprecated tables: %w", err)
	}

	// STEP 1: Create all tables without foreign key constraints
	fmt.Println("STEP 1: Creating all tables without foreign key constraints...")

	// Create a new DB instance with foreign key constraints disabled
	// Use PostgreSQL's session_replication_role to disable all triggers including FK constraints
	dbNoFK := db.Session(&gorm.Session{})
	dbNoFK.Exec("SET session_replication_role = 'replica'")
	dbNoFK.DisableForeignKeyConstraintWhenMigrating = true

	// Auto migrate all models
	fmt.Println("Auto migrating all models...")
	err := dbNoFK.AutoMigrate(
		&models.SystemParam{},
		&models.ProductGroup{},
		&models.Component{},
		&models.Stakeholder{},
		&models.BrandIdentity{},
		&models.NotableSystem{},
		&models.Product{},
		&models.ProductStage{},
		&models.ProductComponent{},
		&models.ProductProductGroup{},
		&models.ProductStageStakeholder{},
		&models.ProductStatusLog{},
		&models.Role{},
		&models.User{},
		&models.ProductUsageStat{},
		&models.ProductFunction{},
		&models.Feature{},
		&models.BusinessFlow{},
		&models.BusinessStep{},
		&models.TechnicalFlow{},
		&models.TechnicalStep{},
		&models.ProductJourney{},
		&models.ProductJourneyComponent{},
	)

	if err != nil {
		return fmt.Errorf("failed to auto migrate tables: %w", err)
	}

	// STEP 2: Add foreign key constraints manually
	fmt.Println("STEP 2: Adding foreign key constraints...")

	// Create a regular session for adding constraints
	dbFK := db.Session(&gorm.Session{})

	// Helper function to safely add a constraint
	addConstraint := func(table, constraintName, constraintDef string) {
		// First try to drop the constraint if it exists
		dbFK.Exec(fmt.Sprintf(
			"DO $$ BEGIN IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = '%s') THEN "+
				"ALTER TABLE %s DROP CONSTRAINT %s; END IF; END $$;",
			constraintName, table, constraintName))

		// Then add the constraint
		dbFK.Exec(fmt.Sprintf("ALTER TABLE %s ADD CONSTRAINT %s %s", table, constraintName, constraintDef))
	}

	// Add foreign keys for Product and ProductStage (circular dependency)
	fmt.Println("Adding foreign key from product_stages to products...")
	addConstraint("product_stages", "fk_product_stages_product",
		"FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE")

	fmt.Println("Adding foreign key from products to product_stages...")
	addConstraint("products", "fk_products_current_stage",
		"FOREIGN KEY (stage_id) REFERENCES product_stages(id)")

	// Add foreign keys for junction tables
	fmt.Println("Adding foreign keys for product_components...")
	addConstraint("product_components", "fk_product_components_product",
		"FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE")
	addConstraint("product_components", "fk_product_components_component",
		"FOREIGN KEY (component_id) REFERENCES components(id) ON DELETE CASCADE")

	fmt.Println("Adding foreign keys for product_product_groups...")
	addConstraint("product_product_groups", "fk_product_product_groups_product",
		"FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE")
	addConstraint("product_product_groups", "fk_product_product_groups_group",
		"FOREIGN KEY (product_group_id) REFERENCES product_groups(id) ON DELETE CASCADE")

	fmt.Println("Adding foreign keys for product_stage_stakeholders...")
	addConstraint("product_stage_stakeholders", "fk_product_stage_stakeholders_stage",
		"FOREIGN KEY (product_stage_id) REFERENCES product_stages(id) ON DELETE CASCADE")
	addConstraint("product_stage_stakeholders", "fk_product_stage_stakeholders_stakeholder",
		"FOREIGN KEY (stakeholder_id) REFERENCES stakeholders(id) ON DELETE CASCADE")

	fmt.Println("Adding foreign keys for product_status_logs...")
	addConstraint("product_status_logs", "fk_product_status_logs_product",
		"FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE")

	// Add foreign key for components self-reference
	fmt.Println("Adding foreign key for components self-reference...")
	addConstraint("components", "fk_component_parent",
		"FOREIGN KEY (parent_component_id) REFERENCES components(id) ON DELETE SET NULL")

	// Add important indexes
	fmt.Println("Adding important indexes...")

	// Helper function to create indexes
	createIndex := func(indexName, tableName, columns string, unique bool) {
		// Drop the index if it exists
		dbFK.Exec(fmt.Sprintf("DROP INDEX IF EXISTS %s", indexName))

		// Create the index
		uniqueSql := ""
		if unique {
			uniqueSql = "UNIQUE "
		}
		dbFK.Exec(fmt.Sprintf("CREATE %sINDEX %s ON %s (%s)", uniqueSql, indexName, tableName, columns))
	}

	// Create composite index for SystemParam
	createIndex("idx_param_type_code", "system_params", "param_type, code", true)

	// Create other important indexes
	createIndex("idx_products_name", "products", "name", false)
	createIndex("idx_products_status", "products", "status", false)
	createIndex("idx_components_name", "components", "name", false)
	createIndex("idx_stakeholders_email", "stakeholders", "email", false)
	createIndex("idx_product_stages_product_id", "product_stages", "product_id", false)
	createIndex("idx_product_stages_stage_code", "product_stages", "stage_code", false)

	// Reset session_replication_role to default
	dbNoFK.Exec("SET session_replication_role = 'origin'")

	fmt.Println("Database migrations completed successfully")
	return nil
}
