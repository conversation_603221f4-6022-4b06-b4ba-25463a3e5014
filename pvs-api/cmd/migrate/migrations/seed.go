package migrations

import (
	"fmt"

	"gorm.io/gorm"
)

// SeedAll thực hiện tất cả các seed functions
func SeedAll(db *gorm.DB) error {
	// Seed SystemParams
	if err := SeedSystemParams(db); err != nil {
		return fmt.Errorf("failed to seed system params: %w", err)
	}

	// Seed Roles
	if err := SeedRoles(db); err != nil {
		return fmt.Errorf("failed to seed roles: %w", err)
	}

	// Seed Users
	if err := SeedUsers(db); err != nil {
		return fmt.Errorf("failed to seed users: %w", err)
	}

	// Seed Brand Identities
	if err := SeedBrandIdentities(db); err != nil {
		return fmt.Errorf("failed to seed brand identities: %w", err)
	}

	// Seed Notable Systems
	if err := SeedNotableSystems(db); err != nil {
		return fmt.Errorf("failed to seed notable systems: %w", err)
	}

	// Seed Components từ Excel
	if err := SeedComponentsFromExcel(db); err != nil {
		return fmt.Errorf("failed to seed components from Excel: %w", err)
	}

	// Seed Products từ Excel
	if err := SeedProductsFromExcel(db); err != nil {
		return fmt.Errorf("failed to seed products from Excel: %w", err)
	}

	// Thêm seed cho ProductUsage
	if err := SeedProductUsage(db); err != nil {
		return fmt.Errorf("failed to seed product usage stats: %w", err)
	}

	fmt.Println("All seed data created successfully")
	return nil
}
