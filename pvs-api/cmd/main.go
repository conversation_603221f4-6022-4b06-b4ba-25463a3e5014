package main

import (
	"fmt"

	"pvs-api/docs"
	"pvs-api/internal/api/middlewares"
	"pvs-api/internal/api/routes"
	"pvs-api/internal/config"
	"pvs-api/internal/db"
	"pvs-api/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @title           Hệ thống Quản lý Sản phẩm API
// @version         1.0
// @description     API cho Hệ thống Quản lý Sản phẩm sử dụng Gin và GORM

// @contact.name   API Support
// @contact.email  <EMAIL>

// @BasePath  /api

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Xác thực bằng JWT Token với tiền tố Bearer. Ví dụ: "Bearer {token}"
func main() {
	// Tải cấu hình
	cfg := config.LoadConfig()

	// Thiết lập logger
	logger := logger.NewLogger()
	defer logger.Sync()

	// Thiết lập mode cho Gin
	if cfg.Server.Mode == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Khởi tạo kết nối database
	database, err := db.SetupDB(&cfg.Database)
	if err != nil {
		logger.Fatal("Failed to initialize database", zap.Error(err))
	}

	// Khởi tạo Gin router
	router := gin.Default()

	router.Use(middlewares.CORSMiddleware())

	// Thiết lập routes
	routes.SetupRoutes(router, database, &cfg.JWT)

	// Khởi động server
	serverAddr := fmt.Sprintf(":%d", cfg.Server.Port)
	logger.Info(fmt.Sprintf("Server started on %s", serverAddr))

	if cfg.Server.SwaggerHost != "" {
		docs.SwaggerInfo.Host = cfg.Server.SwaggerHost
	}

	if err := router.Run(serverAddr); err != nil {
		logger.Fatal("Failed to start server", zap.Error(err))
	}
}
