package utils

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Pagination struct {
	Page    int
	Limit   int
	Filters map[string]interface{}
}

func Paginate(params *Pagination) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if params.Page < 1 {
			params.Page = 1
		}
		if params.Limit < 1 {
			params.Limit = 20
		}

		offset := (params.Page - 1) * params.Limit

		return db.Offset(offset).Limit(params.Limit)
	}
}

func NewPagination(ctx *gin.Context) *Pagination {
	page, err := strconv.Atoi(ctx.Query("page"))
	if err != nil {
		page = 1
	}

	limit, err := strconv.Atoi(ctx.Query("limit"))
	if err != nil {
		limit = 20
	}

	filters := make(map[string]interface{})
	for key, value := range ctx.Request.URL.Query() {
		if key != "page" && key != "limit" {
			filters[key] = value[0]
		}
	}

	return &Pagination{
		Page:    page,
		Limit:   limit,
		Filters: filters,
	}
}
