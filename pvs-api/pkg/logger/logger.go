package logger

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// NewLogger tạo một logger mới với cấu hình phù hợp
func NewLogger() *zap.Logger {
	config := zap.NewProductionConfig()
	config.OutputPaths = []string{"stdout"}
	config.EncoderConfig.TimeKey = "timestamp"
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// Kiểm tra môi trường để thiết lập log level
	logLevel := zap.InfoLevel
	if os.Getenv("SERVER_MODE") == "development" {
		logLevel = zap.DebugLevel
	}
	config.Level = zap.NewAtomicLevelAt(logLevel)

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	return logger
}
