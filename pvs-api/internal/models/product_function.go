package models

import (
    "time"

    "gorm.io/gorm"
)

// ProductFunction represents a major function of a product
type ProductFunction struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    ProductID   uint           `gorm:"not null;index" json:"product_id"`
    Product     *Product       `gorm:"foreignKey:ProductID" json:"product,omitempty"`
    Name        string         `gorm:"size:255;not null" json:"name"`
    Description string         `gorm:"type:text" json:"description"`
    Features    []*Feature     `gorm:"foreignKey:FunctionID" json:"features,omitempty"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}