package models

import (
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User đ<PERSON><PERSON> di<PERSON>n cho một người dùng
type User struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Username  string         `gorm:"uniqueIndex;size:255;not null" json:"username"`
	Password  string         `gorm:"size:255;not null" json:"-"`
	FullName  string         `gorm:"size:255" json:"full_name"`
	RoleCode  string         `gorm:"size:50;not null;index" json:"role_code"`
	IsActive  bool           `gorm:"default:true" json:"is_active"`
	LastLogin *time.Time     `json:"last_login,omitempty"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relations
	Role *Role `gorm:"foreignKey:RoleCode" json:"role,omitempty"`
}

// BeforeSave tính toán và mã hóa mật khẩu trước khi lưu vào cơ sở dữ liệu
func (u *User) BeforeSave(tx *gorm.DB) error {
	if u.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		u.Password = string(hashedPassword)
	}
	return nil
}

// CheckPassword kiểm tra mật khẩu của người dùng
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// HasPermission kiểm tra xem user có permission cụ thể không
func (u *User) HasPermission(permissionCode string) bool {
	if u.Role == nil {
		return false
	}

	return u.Role.HasPermission(permissionCode)
}

// HasAnyPermission kiểm tra xem user có bất kỳ permission nào trong danh sách không
func (u *User) HasAnyPermission(permissionCodes ...string) bool {
	if u.Role == nil {
		return false
	}

	return u.Role.HasAnyPermission(permissionCodes...)
}
