package models

// Permission constants for user management
const (
	PermUserList       = "user:list"
	PermUserCreate     = "user:create"
	PermUserUpdate     = "user:update"
	PermUserDelete     = "user:delete"
	PermUserChangeRole = "user:change-role"
)

// Permission constants for role management
const (
	PermRoleList            = "role:list"
	PermRoleCreate          = "role:create"
	PermRoleUpdate          = "role:update"
	PermRoleDelete          = "role:delete"
	PermRoleManagePermission = "role:manage-permissions"
)

// Permission constants for permission management
const (
	PermPermissionList = "permission:list"
)

// Permission constants for product management
const (
	PermProductList        = "product:list"
	PermProductCreate      = "product:create"
	PermProductUpdate      = "product:update"
	PermProductDelete      = "product:delete"
	PermProductChangeStatus = "product:change-status"
)

// Permission constants for product group management
const (
	PermProductGroupList   = "product-group:list"
	PermProductGroupCreate = "product-group:create"
	PermProductGroupUpdate = "product-group:update"
	PermProductGroupDelete = "product-group:delete"
)

// Permission constants for component management
const (
	PermComponentList   = "component:list"
	PermComponentCreate = "component:create"
	PermComponentUpdate = "component:update"
	PermComponentDelete = "component:delete"
)

// Permission constants for product stage management
const (
	PermProductStageList   = "product-stage:list"
	PermProductStageCreate = "product-stage:create"
	PermProductStageUpdate = "product-stage:update"
	PermProductStageDelete = "product-stage:delete"
	PermProductStageClose  = "product-stage:close"
)

// Permission constants for stakeholder management
const (
	PermStakeholderList   = "stakeholder:list"
	PermStakeholderCreate = "stakeholder:create"
	PermStakeholderUpdate = "stakeholder:update"
	PermStakeholderDelete = "stakeholder:delete"
)

// Permission constants for brand identity management
const (
	PermBrandIdentityList   = "brand-identity:list"
	PermBrandIdentityCreate = "brand-identity:create"
	PermBrandIdentityUpdate = "brand-identity:update"
	PermBrandIdentityDelete = "brand-identity:delete"
)

// Permission constants for notable system management
const (
	PermNotableSystemList   = "notable-system:list"
	PermNotableSystemCreate = "notable-system:create"
	PermNotableSystemUpdate = "notable-system:update"
	PermNotableSystemDelete = "notable-system:delete"
)

// Permission constants for system param management
const (
	PermSystemParamList   = "system-param:list"
	PermSystemParamCreate = "system-param:create"
	PermSystemParamUpdate = "system-param:update"
	PermSystemParamDelete = "system-param:delete"
)

// PermissionGroups maps permission groups to their permissions
var PermissionGroups = map[string][]string{
	"user": {
		PermUserList,
		PermUserCreate,
		PermUserUpdate,
		PermUserDelete,
		PermUserChangeRole,
	},
	"role": {
		PermRoleList,
		PermRoleCreate,
		PermRoleUpdate,
		PermRoleDelete,
		PermRoleManagePermission,
	},
	"permission": {
		PermPermissionList,
	},
	"product": {
		PermProductList,
		PermProductCreate,
		PermProductUpdate,
		PermProductDelete,
		PermProductChangeStatus,
	},
	"product-group": {
		PermProductGroupList,
		PermProductGroupCreate,
		PermProductGroupUpdate,
		PermProductGroupDelete,
	},
	"component": {
		PermComponentList,
		PermComponentCreate,
		PermComponentUpdate,
		PermComponentDelete,
	},
	"product-stage": {
		PermProductStageList,
		PermProductStageCreate,
		PermProductStageUpdate,
		PermProductStageDelete,
		PermProductStageClose,
	},
	"stakeholder": {
		PermStakeholderList,
		PermStakeholderCreate,
		PermStakeholderUpdate,
		PermStakeholderDelete,
	},
	"brand-identity": {
		PermBrandIdentityList,
		PermBrandIdentityCreate,
		PermBrandIdentityUpdate,
		PermBrandIdentityDelete,
	},
	"notable-system": {
		PermNotableSystemList,
		PermNotableSystemCreate,
		PermNotableSystemUpdate,
		PermNotableSystemDelete,
	},
	"system-param": {
		PermSystemParamList,
		PermSystemParamCreate,
		PermSystemParamUpdate,
		PermSystemParamDelete,
	},
}

// AllPermissions returns all permission codes
func AllPermissions() []string {
	var allPerms []string
	for _, perms := range PermissionGroups {
		allPerms = append(allPerms, perms...)
	}
	return allPerms
}

// GetPermissionsByGroup returns all permissions for a specific group
func GetPermissionsByGroup(group string) []string {
	if perms, ok := PermissionGroups[group]; ok {
		return perms
	}
	return []string{}
}

// GetViewPermissions returns all list permissions
func GetViewPermissions() []string {
	var viewPerms []string
	for _, perms := range PermissionGroups {
		for _, perm := range perms {
			if len(perm) >= 5 && perm[len(perm)-5:] == ":list" {
				viewPerms = append(viewPerms, perm)
			}
		}
	}
	return viewPerms
}
