package models

import (
    "time"

    "gorm.io/gorm"
)

// TechnicalFlow represents a technical implementation flow for a business flow
type TechnicalFlow struct {
    ID             uint           `gorm:"primaryKey" json:"id"`
    BusinessFlowID uint           `gorm:"not null;index" json:"business_flow_id"`
    BusinessFlow   *BusinessFlow  `gorm:"foreignKey:BusinessFlowID" json:"business_flow,omitempty"`
    Name           string         `gorm:"size:255;not null" json:"name"`
    Description    string         `gorm:"type:text" json:"description"`
    DiagramURL     string         `gorm:"size:255" json:"diagram_url"`
    Steps          []*TechnicalStep `gorm:"foreignKey:FlowID" json:"steps,omitempty"`
    CreatedAt      time.Time      `json:"created_at"`
    UpdatedAt      time.Time      `json:"updated_at"`
    DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// TechnicalStep represents a step in a technical flow
type TechnicalStep struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    FlowID      uint           `gorm:"not null;index" json:"flow_id"`
    Flow        *TechnicalFlow `gorm:"foreignKey:FlowID" json:"flow,omitempty"`
    StepNumber  int            `gorm:"not null" json:"step_number"`
    Name        string         `gorm:"size:255;not null" json:"name"`
    Description string         `gorm:"type:text" json:"description"`
    ComponentID uint           `gorm:"index" json:"component_id"`
    Component   *Component     `gorm:"foreignKey:ComponentID" json:"component,omitempty"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}