package models

import (
	"time"

	"gorm.io/gorm"
)

// BusinessFlow represents a business process flow for a feature
type BusinessFlow struct {
	ID          uint            `gorm:"primaryKey" json:"id"`
	FeatureID   uint            `gorm:"not null;index" json:"feature_id"`
	Feature     *Feature        `gorm:"foreignKey:FeatureID" json:"feature,omitempty"`
	Name        string          `gorm:"size:255;not null" json:"name"`
	Description string          `gorm:"type:text" json:"description"`
	Steps       []*BusinessStep `gorm:"foreignKey:FlowID" json:"steps,omitempty"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
	DeletedAt   gorm.DeletedAt  `gorm:"index" json:"deleted_at,omitempty"`
}

// BusinessStep represents a step in a business flow
type BusinessStep struct {
	ID              uint           `gorm:"primaryKey" json:"id"`
	FlowID          uint           `gorm:"not null;index" json:"flow_id"`
	Flow            *BusinessFlow  `gorm:"foreignKey:FlowID" json:"flow,omitempty"`
	StepNumber      int            `gorm:"not null" json:"step_number"`
	Name            string         `gorm:"size:255;not null" json:"name"`
	Description     string         `gorm:"type:text" json:"description"`
	ActorDepartment string         `gorm:"size:100;not null" json:"actor_department"`
	ActorRole       string         `gorm:"size:100;not null" json:"actor_role"`
	TechnicalFlowID *uint          `gorm:"index" json:"technical_flow_id,omitempty"`
	TechnicalFlows  *TechnicalFlow `gorm:"foreignKey:TechnicalFlowID" json:"technical_flow,omitempty"`
	CreatedAt       time.Time      `json:"created_at"`
	UpdatedAt       time.Time      `json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}
