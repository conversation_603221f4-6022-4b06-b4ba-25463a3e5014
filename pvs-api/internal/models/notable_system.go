package models

import (
    "time"

    "gorm.io/gorm"
)

// NotableSystem đại diện cho các hệ thống nổi bật đã triển khai
type NotableSystem struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    Name        string         `gorm:"size:255;not null" json:"name"`
    Description string         `gorm:"type:text" json:"description"`
    StartYear   int           `gorm:"not null" json:"start_year"`
    EndYear     *int          `json:"end_year,omitempty"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}