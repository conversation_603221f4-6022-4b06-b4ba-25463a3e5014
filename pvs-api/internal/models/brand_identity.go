package models

import (
	"time"

	"gorm.io/gorm"
)

// BrandIdentity represents company's brand identity history
type BrandIdentity struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	CompanyName string         `gorm:"size:255;not null" json:"company_name"`
	ColorCode   string         `gorm:"size:7;not null" json:"color_code"` // Hex color code e.g. #FF0000
	LogoURL     string         `gorm:"size:255" json:"logo_url"`
	EffectiveAt time.Time      `gorm:"type:date;not null;index" json:"effective_at"`
	ExpiredAt   *time.Time     `gorm:"type:date" json:"expired_at,omitempty"`
	Note        string         `gorm:"type:text" json:"note"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// IsActive checks if this brand identity is currently active
func (b *BrandIdentity) IsActive() bool {
	now := time.Now()
	return b.EffectiveAt.Before(now) && (b.ExpiredAt == nil || b.ExpiredAt.After(now))
}
