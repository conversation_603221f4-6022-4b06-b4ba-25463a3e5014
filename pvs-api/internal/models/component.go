package models

import (
	"time"

	"gorm.io/gorm"
)

// Component đại diện cho một hệ thống/thành phần
type Component struct {
	ID                uint           `gorm:"primaryKey" json:"id"`
	Name              string         `gorm:"size:255;not null" json:"name"`
	Description       string         `gorm:"type:text" json:"description"`
	ComponentTypeCode string         `gorm:"size:50;not null" json:"component_type_code"`
	TargetUserCode    string         `gorm:"size:50;not null" json:"target_user_code"`
	Importance        string         `gorm:"type:text;not null" json:"importance"`
	MainFunction      string         `gorm:"size:255" json:"main_function"`
	ExtraFunction     string         `gorm:"size:255" json:"extra_function"`
	ParentComponentID *uint          `gorm:"index" json:"parent_component_id,omitempty"`
	ParentComponent   *Component     `gorm:"foreignKey:ParentComponentID" json:"parent_component,omitempty"`
	ChildComponents   []*Component   `gorm:"foreignKey:ParentComponentID" json:"child_components,omitempty"`
	ConfluenceURL     string         `gorm:"size:255" json:"confluence_url"`
	Products          []*Product     `gorm:"many2many:product_components;" json:"products,omitempty"`
	ColorCode         string         `gorm:"size:7" json:"color_code"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// ProductComponent là bảng liên kết giữa Product và Component
type ProductComponent struct {
	ProductID   uint `gorm:"primaryKey" json:"product_id"`
	ComponentID uint `gorm:"primaryKey" json:"component_id"`
}
