package models

import (
	"time"

	"gorm.io/gorm"
)

// Product đại diện cho một sản phẩm
type Product struct {
	ID            uint            `gorm:"primaryKey" json:"id"`
	Name          string          `gorm:"size:255;not null;unique" json:"name"`
	Description   string          `gorm:"type:text" json:"description"`
	Status        string          `gorm:"not null;size:20" json:"status"`
	ProductGroups []*ProductGroup `gorm:"many2many:product_product_groups;" json:"product_groups,omitempty"`
	Components    []*Component    `gorm:"many2many:product_components;" json:"components,omitempty"`
	ProductStages []*ProductStage `gorm:"foreignKey:ProductID" json:"product_stages,omitempty"`
	StageID       *uint           `gorm:"null;index" json:"stage_id"`
	CurrentStage  *ProductStage   `gorm:"foreignKey:StageID" json:"current_stage,omitempty"`
	IsFavorite    bool            `gorm:"default:false" json:"is_favorite"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
	DeletedAt     gorm.DeletedAt  `gorm:"index" json:"deleted_at,omitempty"`
}
