package models

import (
	"time"

	"gorm.io/gorm"
)

// ProductJourney đại diện cho một hành trình khách hàng của sản phẩm
type ProductJourney struct {
	ID          uint                       `gorm:"primaryKey" json:"id"`
	Name        string                     `gorm:"size:255;not null" json:"name"`
	Description string                     `gorm:"type:text" json:"description"`
	ProductID   uint                       `gorm:"not null;index" json:"product_id"`
	Product     *Product                   `gorm:"foreignKey:ProductID" json:"product,omitempty"`
	FlowData    string                     `gorm:"type:jsonb" json:"flow_data"` // JSON mô tả luồng hành trình với cấu trúc components, actions, steps
	Components  []*ProductJourneyComponent `gorm:"foreignKey:JourneyID" json:"components,omitempty"`
	Status      string                     `gorm:"size:50;not null;default:'ACTIVE'" json:"status"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	DeletedAt   gorm.DeletedAt             `gorm:"index" json:"deleted_at,omitempty"`
}

// ProductJourneyComponent đại diện cho component được phép tác động vào hành trình
type ProductJourneyComponent struct {
	ID          uint            `gorm:"primaryKey" json:"id"`
	JourneyID   uint            `gorm:"not null;index" json:"journey_id"`
	Journey     *ProductJourney `gorm:"foreignKey:JourneyID" json:"journey,omitempty"`
	ComponentID uint            `gorm:"not null;index" json:"component_id"`
	Component   *Component      `gorm:"foreignKey:ComponentID" json:"component,omitempty"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}
