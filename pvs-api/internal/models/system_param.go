package models

import (
	"time"

	"gorm.io/gorm"
)

// <PERSON><PERSON><PERSON> lo<PERSON>i tham số hệ thống
type ParamType string

const (
	ParamTypeComponentType   ParamType = "COMPONENT_TYPE"   // Lo<PERSON><PERSON> hệ thống/thành phần
	ParamTypeTargetUser      ParamType = "TARGET_USER"      // Nhóm người dùng sử dụng hệ thống
	ParamTypeStageType       ParamType = "STAGE_TYPE"       // Giai đoạn của sản phẩm
	ParamTypeProductStatus   ParamType = "PRODUCT_STATUS"   // Trạng thái của sản phẩm
	ParamTypeStakeholderRole ParamType = "STAKEHOLDER_ROLE" // Vai trò của người tham gia sản phẩm
)

// SystemParam đại diện cho một tham số hệ thống
type SystemParam struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	ParamType    ParamType      `gorm:"size:50;not null" json:"param_type"` // Lo<PERSON>i tham số
	Code         string         `gorm:"size:50;not null" json:"code"`       // Mã tham số
	Name         string         `gorm:"size:255;not null" json:"name"`      // Tên/mô tả của tham số
	DisplayOrder int            `gorm:"default:0" json:"display_order"`     // Thứ tự hiển thị
	IsActive     bool           `gorm:"default:true" json:"is_active"`      // Trạng thái kích hoạt
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Composite index cho ParamType và Code
	_ string `gorm:"index:idx_param_type_code,unique,composite:param_type_code;column:param_type,code"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (SystemParam) TableName() string {
	return "system_params"
}
