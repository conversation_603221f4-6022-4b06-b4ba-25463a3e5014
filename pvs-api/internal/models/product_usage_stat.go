package models

import (
    "time"
)

type ProductUsageStat struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    ProductID uint      `json:"product_id"`
    Product   Product   `json:"product" gorm:"foreignKey:ProductID"`
    Month     time.Time `json:"month" gorm:"type:date"` // Chỉ lưu năm-tháng
    Users     int       `json:"users"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}