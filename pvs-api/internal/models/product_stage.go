package models

import (
	"time"

	"gorm.io/gorm"
)

// ProductStage đại diện cho giai đoạn của một sản phẩm
type ProductStage struct {
	ID           uint                       `gorm:"primaryKey" json:"id"`
	ProductID    uint                       `gorm:"not null;index" json:"product_id"`
	Product      *Product                   `gorm:"foreignKey:ProductID" json:"product,omitempty"`
	StageCode    string                     `gorm:"size:50;not null" json:"stage_code"`
	StartDate    time.Time                  `json:"start_date"`
	EndDate      *time.Time                 `json:"end_date,omitempty"`
	Notes        string                     `gorm:"type:text" json:"notes"`
	Stakeholders []*ProductStageStakeholder `gorm:"foreignKey:ProductStageID" json:"stakeholders,omitempty"`
	CreatedAt    time.Time                  `json:"created_at"`
	UpdatedAt    time.Time                  `json:"updated_at"`
	DeletedAt    gorm.DeletedAt             `gorm:"index" json:"deleted_at,omitempty"`
}

// ProductStageStakeholder là bảng liên kết giữa ProductStage và Stakeholder
type ProductStageStakeholder struct {
	ProductStageID uint           `gorm:"primaryKey" json:"product_stage_id"`
	ProductStage   *ProductStage  `gorm:"foreignKey:ProductStageID" json:"product_stage,omitempty"`
	StakeholderID  uint           `gorm:"primaryKey" json:"stakeholder_id"`
	Stakeholder    *Stakeholder   `gorm:"foreignKey:StakeholderID" json:"stakeholder,omitempty"`
	RoleCode       string         `gorm:"size:50;not null" json:"role_code"`
	Tasks          string         `gorm:"type:text" json:"tasks"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}
