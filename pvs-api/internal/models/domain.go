package models

import (
	"time"
)

// Domain đại diện cho một domain nhỏ mà một hoặc nhiều developer cù<PERSON> phát triển
type Domain struct {
	ID             uint      `gorm:"primaryKey" json:"id"`
	Name           string    `gorm:"size:255;not null" json:"name"`
	ParentDomainID *uint     `gorm:"index" json:"parent_domain_id,omitempty"`
	ParentDomain   *Domain   `gorm:"foreignKey:ParentDomainID" json:"parent_domain,omitempty"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}
