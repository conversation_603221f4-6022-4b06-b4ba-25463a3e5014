package models

import (
	"time"

	"gorm.io/gorm"
)

// ProductGroup đại diện cho một nhóm sản phẩm
type ProductGroup struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	Name        string         `gorm:"size:255;not null;unique" json:"name"`
	Description string         `gorm:"type:text" json:"description"`
	IsFavorite  bool           `gorm:"default:false" json:"is_favorite"`
	Products    []*Product     `gorm:"many2many:product_product_groups;" json:"products,omitempty"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}

// ProductProductGroup là bảng liên kết giữa Product và ProductGroup
type ProductProductGroup struct {
	ProductID      uint `gorm:"primaryKey" json:"product_id"`
	ProductGroupID uint `gorm:"primaryKey" json:"product_group_id"`
}
