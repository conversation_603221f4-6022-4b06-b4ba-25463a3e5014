package models

import (
    "time"

    "gorm.io/gorm"
)

// Feature represents a specific feature within a product function
type Feature struct {
    ID          uint            `gorm:"primaryKey" json:"id"`
    FunctionID  uint            `gorm:"not null;index" json:"function_id"`
    Function    *ProductFunction `gorm:"foreignKey:FunctionID" json:"function,omitempty"`
    Name        string          `gorm:"size:255;not null" json:"name"`
    Description string          `gorm:"type:text" json:"description"`
    Status      string          `gorm:"size:50;not null;default:'DRAFT'" json:"status"`
    BusinessFlows []*BusinessFlow `gorm:"foreignKey:FeatureID" json:"business_flows,omitempty"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
    DeletedAt   gorm.DeletedAt  `gorm:"index" json:"deleted_at,omitempty"`
}