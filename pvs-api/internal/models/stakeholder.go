package models

import (
	"time"

	"gorm.io/gorm"
)

// Stakeholder đại diện cho một người phụ trách
type Stakeholder struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	FullName  string         `gorm:"size:255;not null" json:"full_name"`
	Email     string         `gorm:"size:255;not null;unique" json:"email"`
	Position  string         `gorm:"size:255" json:"position"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`
}
