package models

import (
	"time"
)

// ProductStatusLog represents a log entry for product status changes
type ProductStatusLog struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	ProductID uint      `gorm:"not null;index" json:"product_id"`
	Product   *Product  `gorm:"foreignKey:ProductID" json:"product,omitempty"`
	Status    string    `gorm:"not null;size:20" json:"status"`
	ChangedBy string    `gorm:"size:255" json:"changed_by"`
	ChangedAt time.Time `gorm:"type:date" json:"changed_at"`
	Note      string    `gorm:"size:255" json:"description"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
