package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

// Role định nghĩa vai trò người dùng
type Role struct {
	Code        string         `gorm:"primaryKey;size:50" json:"code"`
	Name        string         `gorm:"size:255;not null" json:"name"`
	Description string         `gorm:"type:text" json:"description"`
	IsSystem    bool           `gorm:"default:false" json:"is_system"`        // Đ<PERSON>h dấu role hệ thống, không được xóa
	Permissions pq.StringArray `gorm:"type:varchar(50)[]" json:"permissions"` // Mảng các permission codes
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// Relations
	Users []*User `gorm:"foreignKey:RoleCode" json:"users,omitempty"`
}

// HasPermission kiểm tra xem role có permission cụ thể không
func (r *Role) HasPermission(permissionCode string) bool {
	for _, code := range r.Permissions {
		if code == permissionCode {
			return true
		}
	}
	return false
}

// HasAnyPermission kiểm tra xem role có bất kỳ permission nào trong danh sách không
func (r *Role) HasAnyPermission(permissionCodes ...string) bool {
	for _, code := range permissionCodes {
		if r.HasPermission(code) {
			return true
		}
	}
	return false
}
