package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductGroupController struct {
	repo repositories.ProductGroupRepository
}

func NewProductGroupController(db *gorm.DB) *ProductGroupController {
	return &ProductGroupController{
		repo: repositories.NewProductGroupRepository(db),
	}
}

// ListProductGroups godoc
// @Summary      Lấy danh sách tất cả nhóm sản phẩm
// @Description  Trả về danh sách tất cả nhóm sản phẩm trong hệ thống
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-groups [get]
func (c *ProductGroupController) ListProductGroups(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	productGroups, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product groups", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Product groups retrieved successfully", productGroups, total)
}

// GetProductGroupByID godoc
// @Summary      Lấy thông tin một nhóm sản phẩm
// @Description  Trả về thông tin chi tiết của một nhóm sản phẩm theo ID
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID nhóm sản phẩm"
// @Success      200  {object}  utils.Response{data=pvs-api_internal_models.ProductGroup}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /product-groups/{id} [get]
func (c *ProductGroupController) GetProductGroupByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	productGroup, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product group not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product group retrieved successfully", productGroup)
}

// CreateProductGroup godoc
// @Summary      Tạo nhóm sản phẩm mới
// @Description  Tạo một nhóm sản phẩm mới với thông tin được cung cấp
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        productGroup  body      dto.ProductGroupRequest  true  "Thông tin nhóm sản phẩm"
// @Success      201  {object}  pvs-api_internal_models.ProductGroup
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-groups [post]
func (c *ProductGroupController) CreateProductGroup(ctx *gin.Context) {
	var request dto.ProductGroupRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Chuyển đổi từ DTO sang model
	productGroup := request.ToProductGroupModel()

	err := c.repo.Create(ctx, productGroup)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product group", err.Error())
		return
	}

	response := dto.FromProductGroupModel(productGroup)

	utils.SuccessResponse(ctx, http.StatusCreated, "Product group created successfully", response)
}

// UpdateProductGroup godoc
// @Summary      Cập nhật nhóm sản phẩm
// @Description  Cập nhật thông tin của một nhóm sản phẩm theo ID
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id            path      int                  true  "ID nhóm sản phẩm"
// @Param        productGroup  body      pvs-api_internal_models.ProductGroup  true  "Thông tin nhóm sản phẩm"
// @Success      200  {object}  pvs-api_internal_models.ProductGroup
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-groups/{id} [put]
func (c *ProductGroupController) UpdateProductGroup(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	productGroup, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product group not found", err.Error())
		return
	}

	if err := ctx.ShouldBindJSON(&productGroup); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	productGroup.ID = uint(id)
	err = c.repo.Update(ctx, productGroup)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product group", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product group updated successfully", productGroup)
}

// DeleteProductGroup godoc
// @Summary      Xóa nhóm sản phẩm
// @Description  Xóa một nhóm sản phẩm theo ID
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID nhóm sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-groups/{id} [delete]
func (c *ProductGroupController) DeleteProductGroup(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete product group", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product group deleted successfully", nil)
}

// ToggleProductGroupFavorite godoc
// @Summary      Toggle trạng thái yêu thích của nhóm sản phẩm
// @Description  Cập nhật trạng thái yêu thích (favorite) của một nhóm sản phẩm
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int  true  "ID nhóm sản phẩm"
// @Param        is_favorite  query     bool true  "Trạng thái yêu thích (true/false)"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-groups/{id}/favorite [patch]
func (c *ProductGroupController) ToggleProductGroupFavorite(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	isFavoriteStr := ctx.Query("is_favorite")
	if isFavoriteStr == "" {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "is_favorite parameter is required", "")
		return
	}

	isFavorite, err := strconv.ParseBool(isFavoriteStr)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid is_favorite value, must be true or false", err.Error())
		return
	}

	// Kiểm tra nhóm sản phẩm tồn tại
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to toggle product group favorite", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product group not found", "")
		return
	}

	// Cập nhật trạng thái favorite
	err = c.repo.UpdateFavoriteStatus(ctx, uint(id), isFavorite)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to toggle product group favorite", err.Error())
		return
	}

	var message string
	if isFavorite {
		message = "Product group marked as favorite successfully"
	} else {
		message = "Product group unmarked as favorite successfully"
	}

	utils.SuccessResponse(ctx, http.StatusOK, message, nil)
}

// GetProductGroupWithProducts godoc
// @Summary      Lấy các sản phẩm liên quan nhóm sản phẩm
// @Description  Trả về danh sách các sản phẩm thuộc nhóm sản phẩm
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID nhóm sản phẩm"
// @Success      200  {array}   pvs-api_internal_models.Product
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /product-groups/{id}/products [get]
func (c *ProductGroupController) ListProductsByGroupID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	products, err := c.repo.ListProducts(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product group not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Products retrieved successfully", products)
}

// ListForDropdown godoc
// @Summary      Lấy danh sách product group cho dropdown
// @Description  Trả về danh sách id và tên của product groups, có thể lọc theo từ khóa
// @Tags         Product-Groups
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        search  query     string  false  "Search keyword"
// @Success      200    {object}  utils.Response{data=[]dto.DropdownItem}
// @Failure      401    {object}  utils.Response "Unauthorized"
// @Failure      500    {object}  utils.Response
// @Router       /product-groups/dropdown [get]
func (c *ProductGroupController) ListForDropdown(ctx *gin.Context) {
	search := ctx.Query("search")

	items, err := c.repo.ListForDropdown(ctx, search)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch dropdown items", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Dropdown items retrieved successfully", items)
}
