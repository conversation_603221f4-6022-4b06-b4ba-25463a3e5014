package controllers

import (
	"errors"
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SystemParamController xử lý các request liên quan đến tham số hệ thống
type SystemParamController struct {
	repo repositories.SystemParamRepository
}

// NewSystemParamController tạo một instance mới của SystemParamController
func NewSystemParamController(db *gorm.DB) *SystemParamController {
	return &SystemParamController{
		repo: repositories.NewSystemParamRepository(db),
	}
}

// GetAllSystemParams godoc
// @Summary      Lấy danh sách tham số hệ thống
// @Description  Trả về danh sách tham số hệ thống có phân trang
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page       query    int     false  "Số trang"
// @Param        limit      query    int     false  "Số lượng mỗi trang"
// @Param        type       query    string  false  "Lọc theo loại tham số"
// @Param        is_active  query    bool    false  "Lọc theo trạng thái kích hoạt"
// @Success      200        {object}  utils.Response{data=[]dto.SystemParamResponse}
// @Failure      401        {object}  utils.Response "Unauthorized"
// @Failure      500        {object}  utils.Response "Internal Server Error"
// @Router       /system-params [get]
func (c *SystemParamController) GetAllSystemParams(ctx *gin.Context) {
	pagination := utils.NewPagination(ctx)
	if pagination.Filters == nil {
		pagination.Filters = make(map[string]interface{})
	}

	// Xử lý tham số is_active
	if isActiveStr := ctx.Query("is_active"); isActiveStr != "" {
		isActive := isActiveStr == "true"
		pagination.Filters["is_active"] = isActive
	}

	// Xử lý tham số type (param_type)
	if paramType := ctx.Query("type"); paramType != "" {
		pagination.Filters["param_type"] = paramType
	}

	// Lấy danh sách params với các bộ lọc
	params, total, err := c.repo.List(ctx, pagination)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch system params", err.Error())
		return
	}

	// Chuyển đổi sang response DTO
	var responses []*dto.SystemParamResponse
	for _, param := range params {
		responses = append(responses, dto.FromSystemParamModel(param))
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "System params retrieved successfully", responses, total)
}

// GetSystemParamByID godoc
// @Summary      Lấy thông tin tham số hệ thống theo ID
// @Description  Trả về thông tin chi tiết của một tham số hệ thống
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID tham số hệ thống"
// @Success      200  {object}  utils.Response{data=dto.SystemParamResponse}
// @Failure      400  {object}  utils.Response "Bad Request"
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response "Not Found"
// @Router       /system-params/{id} [get]
func (c *SystemParamController) GetSystemParamByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	param, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "System parameter not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "System parameter retrieved successfully", dto.FromSystemParamModel(param))
}

// CreateSystemParam godoc
// @Summary      Tạo tham số hệ thống mới
// @Description  Tạo một tham số hệ thống mới với thông tin được cung cấp
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        systemParam  body      dto.SystemParamRequest  true  "Thông tin tham số hệ thống"
// @Success      201          {object}  utils.Response{data=dto.SystemParamResponse}
// @Failure      400          {object}  utils.Response "Bad Request"
// @Failure      401          {object}  utils.Response "Unauthorized"
// @Failure      500          {object}  utils.Response "Internal Server Error"
// @Router       /system-params [post]
func (c *SystemParamController) CreateSystemParam(ctx *gin.Context) {
	var request dto.SystemParamRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem mã đã tồn tại chưa
	existingParam, err := c.repo.GetByCode(ctx, request.ParamType, request.Code)
	if err == nil && existingParam != nil {
		err = errors.New("parameter with this code already exists")
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Parameter with this code already exists", err.Error())
		return
	}

	param := request.ToSystemParamModel()
	if err := c.repo.Create(ctx, param); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create system parameter", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "System parameter created successfully", dto.FromSystemParamModel(param))
}

// UpdateSystemParam godoc
// @Summary      Cập nhật tham số hệ thống
// @Description  Cập nhật thông tin của một tham số hệ thống
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int                     true  "ID tham số hệ thống"
// @Param        systemParam  body      dto.SystemParamRequest  true  "Thông tin tham số hệ thống"
// @Success      200          {object}  utils.Response{data=dto.SystemParamResponse}
// @Failure      400          {object}  utils.Response "Bad Request"
// @Failure      401          {object}  utils.Response "Unauthorized"
// @Failure      404          {object}  utils.Response "Not Found"
// @Failure      500          {object}  utils.Response "Internal Server Error"
// @Router       /system-params/{id} [put]
func (c *SystemParamController) UpdateSystemParam(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	var request dto.SystemParamRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem tham số có tồn tại không
	existingParam, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "System parameter not found", err.Error())
		return
	}

	// Kiểm tra xem mã mới đã tồn tại chưa (nếu thay đổi mã)
	if existingParam.Code != request.Code || existingParam.ParamType != request.ParamType {
		paramWithSameCode, err := c.repo.GetByCode(ctx, request.ParamType, request.Code)
		if err == nil && paramWithSameCode != nil && paramWithSameCode.ID != uint(id) {
			err = errors.New("parameter with this code already exists")
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Parameter with this code already exists", err.Error())
			return
		}
	}

	// Cập nhật thông tin
	existingParam.ParamType = request.ParamType
	existingParam.Code = request.Code
	existingParam.Name = request.Name
	existingParam.DisplayOrder = request.DisplayOrder
	existingParam.IsActive = request.IsActive

	if err := c.repo.Update(ctx, existingParam); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update system parameter", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "System parameter updated successfully", dto.FromSystemParamModel(existingParam))
}

// DeleteSystemParam godoc
// @Summary      Xóa tham số hệ thống
// @Description  Xóa một tham số hệ thống khỏi hệ thống
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID tham số hệ thống"
// @Success      200  {object}  utils.Response "OK"
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response "Not Found"
// @Failure      500  {object}  utils.Response "Internal Server Error"
// @Router       /system-params/{id} [delete]
func (c *SystemParamController) DeleteSystemParam(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem tham số có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "System parameter not found", err.Error())
		return
	}

	if err := c.repo.Delete(ctx, uint(id)); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete system parameter", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "System parameter deleted successfully", nil)
}

// GetAllParamTypes godoc
// @Summary      Lấy danh sách tất cả loại tham số
// @Description  Trả về danh sách tất cả các loại tham số hệ thống
// @Tags         System-Params
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response{data=[]string}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response "Internal Server Error"
// @Router       /system-params/types [get]
func (c *SystemParamController) GetAllParamTypes(ctx *gin.Context) {
	// Get all param types from the models package
	paramTypes := []string{
		string(models.ParamTypeComponentType),
		string(models.ParamTypeTargetUser),
		string(models.ParamTypeStageType),
		string(models.ParamTypeProductStatus),
		string(models.ParamTypeStakeholderRole),
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Parameter types retrieved successfully", paramTypes)
}
