package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ComponentController struct {
	repo            repositories.ComponentRepository
	systemParamRepo repositories.SystemParamRepository
}

func NewComponentController(db *gorm.DB) *ComponentController {
	return &ComponentController{
		repo: repositories.NewComponentRepository(db),
	}
}

// ListComponents godoc
// @Summary      Lấy danh sách tất cả thành phần hệ thống
// @Description  Trả về danh sách tất cả thành phần hệ thống có phân trang
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page      query    int     false  "Số trang"
// @Param        limit     query    int     false  "Số lượng mỗi trang"
// @Param        parent_id query    int     false  "Lọc theo parent ID"
// @Success      200  {object}  utils.Response{data=[]pvs-api_internal_dto.ComponentResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /components [get]
func (c *ComponentController) ListComponents(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	components, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch components", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Components retrieved successfully", components, total)
}

// GetComponentByID godoc
// @Summary      Lấy thông tin một thành phần hệ thống
// @Description  Trả về thông tin chi tiết của một thành phần hệ thống theo ID
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID thành phần"
// @Success      200  {object}  models.Component
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /components/{id} [get]
func (c *ComponentController) GetComponentByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	component, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Component not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component retrieved successfully", component)
}

// CreateComponent godoc
// @Summary      Tạo thành phần hệ thống mới
// @Description  Tạo một thành phần hệ thống mới với thông tin được cung cấp
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        component  body      models.Component  true  "Thông tin thành phần"
// @Success      201  {object}  models.Component
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /components [post]
func (c *ComponentController) CreateComponent(ctx *gin.Context) {
	var component models.Component
	if err := ctx.ShouldBindJSON(&component); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Validate ComponentTypeCode
	componentType, err := c.systemParamRepo.GetByCode(ctx, models.ParamTypeComponentType, component.ComponentTypeCode)
	if err != nil || componentType == nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid component_type_code", err.Error())
		return
	}

	// Validate TargetUserCode
	targetUser, err := c.systemParamRepo.GetByCode(ctx, models.ParamTypeTargetUser, component.TargetUserCode)
	if err != nil || targetUser == nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid target_user_code", err.Error())
		return
	}

	// Xử lý trường hợp ParentComponentID được truyền vào
	if component.ParentComponentID != nil {
		// Kiểm tra xem parent component có tồn tại không
		_, err := c.repo.GetByID(ctx, *component.ParentComponentID)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusNotFound, "Parent component not found", err.Error())
			return
		}
	}

	err = c.repo.Create(ctx, &component)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create component", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Component created successfully", component)
}

// UpdateComponent godoc
// @Summary      Cập nhật thành phần hệ thống
// @Description  Cập nhật thông tin của một thành phần hệ thống theo ID
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id        path      int                 true  "ID thành phần"
// @Param        component body      models.Component    true  "Thông tin thành phần"
// @Success      200  {object}  models.Component
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /components/{id} [put]
func (c *ComponentController) UpdateComponent(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem component có tồn tại không
	exists, err := c.repo.ExistByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update component", err.Error())
		return
	}

	if !exists {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Component not found", err.Error())
		return
	}

	// Bind dữ liệu từ request
	var component models.Component
	if err := ctx.ShouldBindJSON(&component); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Gán ID của component hiện tại
	component.ID = uint(id)

	// Xử lý trường hợp ParentComponentID được truyền vào
	if component.ParentComponentID != nil {
		// Kiểm tra xem parent component có tồn tại không
		_, err := c.repo.GetByID(ctx, *component.ParentComponentID)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusNotFound, "Parent component not found", err.Error())
			return
		}

		// Kiểm tra xem parent component có phải là chính nó không
		if *component.ParentComponentID == component.ID {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Component cannot reference itself", err.Error())
			return
		}
	}

	err = c.repo.Update(ctx, &component)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update component", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component updated successfully", component)
}

// DeleteComponent godoc
// @Summary      Xóa thành phần hệ thống
// @Description  Xóa một thành phần hệ thống theo ID
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID thành phần"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /components/{id} [delete]
func (c *ComponentController) DeleteComponent(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete component", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Component deleted successfully", nil)
}

// GetChildComponents godoc
// @Summary      Lấy danh sách các thành phần con
// @Description  Trả về danh sách các thành phần con của một thành phần hệ thống
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID thành phần cha"
// @Success      200  {array}   models.Component
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /components/{id}/children [get]
func (c *ComponentController) GetChildComponents(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	components, err := c.repo.GetChildComponents(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Component not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Child components retrieved successfully", components)
}

// ListForDropdown godoc
// @Summary      Lấy danh sách component cho dropdown
// @Description  Trả về danh sách id và tên của components, có thể lọc theo từ khóa
// @Tags         Components
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        search  query     string  false  "Search keyword"
// @Success      200    {object}  utils.Response{data=[]pvs-api_internal_dto.DropdownItem}
// @Failure      401    {object}  utils.Response "Unauthorized"
// @Failure      500    {object}  utils.Response
// @Router       /components/dropdown [get]
func (c *ComponentController) ListForDropdown(ctx *gin.Context) {
	search := ctx.Query("search")

	items, err := c.repo.ListForDropdown(ctx, search)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch dropdown items", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Dropdown items retrieved successfully", items)
}
