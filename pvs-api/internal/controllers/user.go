package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserController struct {
	userRepo repositories.UserRepository
	roleRepo repositories.RoleRepository
}

func NewUserController(db *gorm.DB) *UserController {
	return &UserController{
		userRepo: repositories.NewUserRepository(db),
		roleRepo: repositories.NewRoleRepository(db),
	}
}

// ListUsers godoc
// @Summary      Lấy danh sách người dùng
// @Description  Trả về danh sách người dùng có phân trang
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page      query    int     false  "Số trang"
// @Param        limit     query    int     false  "Số lượng mỗi trang"
// @Param        is_active query    bool    false  "Lọc theo trạng thái kích hoạt"
// @Param        role_code query    string  false  "Lọc theo role code"
// @Success      200  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /users [get]
func (c *UserController) ListUsers(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	users, total, err := c.userRepo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list users", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Users retrieved successfully", users, total)
}

// GetUserByID godoc
// @Summary      Lấy thông tin người dùng theo ID
// @Description  Trả về thông tin chi tiết của một người dùng
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "User ID"
// @Success      200  {object}  utils.Response{data=models.User}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /users/{id} [get]
func (c *UserController) GetUserByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	user, err := c.userRepo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "User not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "User retrieved successfully", user)
}

// CreateUser godoc
// @Summary      Tạo người dùng mới
// @Description  Tạo một người dùng mới trong hệ thống
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        user  body      dto.CreateUserRequest  true  "Thông tin người dùng"
// @Success      201  {object}  utils.Response{data=models.User}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /users [post]
func (c *UserController) CreateUser(ctx *gin.Context) {
	var request dto.CreateUserRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra username đã tồn tại
	existingUser, _ := c.userRepo.GetByUsername(ctx, request.Username)
	if existingUser != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Username already exists", "")
		return
	}

	// Kiểm tra email đã tồn tại
	existingUser, _ = c.userRepo.GetByEmail(ctx, request.Email)
	if existingUser != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Email already exists", "")
		return
	}

	user := &models.User{
		Username: request.Username,
		Password: request.Password,
		FullName: request.FullName,
		RoleCode: request.RoleCode,
		IsActive: true,
	}

	if err := c.userRepo.Create(ctx, user); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create user", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "User created successfully", user)
}

// UpdateUser godoc
// @Summary      Cập nhật thông tin người dùng
// @Description  Cập nhật thông tin của một người dùng
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id    path      int                  true  "User ID"
// @Param        user  body      dto.UpdateUserRequest  true  "Thông tin người dùng"
// @Success      200  {object}  utils.Response{data=models.User}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /users/{id} [put]
func (c *UserController) UpdateUser(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	var request dto.UpdateUserRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra user có tồn tại
	existingUser, err := c.userRepo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "User not found", err.Error())
		return
	}

	existingUser.FullName = request.FullName
	existingUser.IsActive = request.IsActive

	if err := c.userRepo.Update(ctx, existingUser); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update user", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "User updated successfully", existingUser)
}

// ChangePassword godoc
// @Summary      Đổi mật khẩu người dùng
// @Description  Cập nhật mật khẩu của một người dùng
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id    path      int                  true  "User ID"
// @Param        passwords  body  dto.ChangePasswordRequest  true  "Thông tin mật khẩu"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /users/{id}/password [patch]
func (c *UserController) ChangePassword(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	var request dto.ChangePasswordRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra user có tồn tại
	user, err := c.userRepo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "User not found", err.Error())
		return
	}

	// Kiểm tra mật khẩu cũ
	if !user.CheckPassword(request.OldPassword) {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid old password", "")
		return
	}

	if err := c.userRepo.ChangePassword(ctx, uint(id), request.NewPassword); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to change password", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Password changed successfully", nil)
}

// UpdateUserRole godoc
// @Summary      Cập nhật role của người dùng
// @Description  Thay đổi role của một người dùng
// @Tags         Users
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id    path      int                  true  "User ID"
// @Param        role  body      dto.UpdateUserRoleRequest  true  "Thông tin role"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /users/{id}/role [patch]
func (c *UserController) UpdateUserRole(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	var request dto.UpdateUserRoleRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra user có tồn tại
	if _, err := c.userRepo.GetByID(ctx, uint(id)); err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "User not found", err.Error())
		return
	}

	if err := c.userRepo.UpdateRole(ctx, uint(id), request.RoleCode); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update user role", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "User role updated successfully", nil)
}
