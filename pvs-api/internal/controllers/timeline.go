package controllers

import (
	"net/http"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TimelineController struct {
	repo repositories.TimelineRepository
}

func NewTimelineController(db *gorm.DB) *TimelineController {
	return &TimelineController{
		repo: repositories.NewTimelineRepository(db),
	}
}

// GetTimeline godoc
// @Summary      Get product timeline
// @Description  Returns a timeline view of all product groups with their products, stages, status logs, and components
// @Tags         Timeline
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response{data=pvs-api_internal_dto.TimelineResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response "Internal Server Error"
// @Router       /timeline [get]
func (c *TimelineController) GetTimeline(ctx *gin.Context) {
	timeline, err := c.repo.GetTimeline(ctx)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch timeline", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Timeline retrieved successfully", timeline)
}
