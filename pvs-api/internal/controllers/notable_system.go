package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type NotableSystemController struct {
	repo repositories.NotableSystemRepository
}

func NewNotableSystemController(db *gorm.DB) *NotableSystemController {
	return &NotableSystemController{
		repo: repositories.NewNotableSystemRepository(db),
	}
}

// ListNotableSystems godoc
// @Summary      Lấy danh sách các hệ thống nổi bật
// @Description  Trả về danh sách các hệ thống nổi bật đã triển khai
// @Tags         Notable-Systems
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        year  query     int  false  "Lọ<PERSON> theo năm"
// @Success      200   {object}  utils.Response{data=[]dto.NotableSystemResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500   {object}  utils.Response "Internal Server Error"
// @Router       /notable-systems [get]
func (c *NotableSystemController) ListNotableSystems(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	// Add year filter if provided
	if yearStr := ctx.Query("year"); yearStr != "" {
		if year, err := strconv.Atoi(yearStr); err == nil {
			if params.Filters == nil {
				params.Filters = make(map[string]interface{})
			}
			params.Filters["year"] = year
		}
	}

	systems, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch notable systems", err.Error())
		return
	}

	var responses []dto.NotableSystemResponse
	for _, system := range systems {
		responses = append(responses, *dto.FromNotableSystemModel(system))
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Notable systems retrieved successfully", responses, total)
}

// GetNotableSystemByID godoc
// @Summary      Lấy chi tiết một hệ thống nổi bật
// @Description  Trả về thông tin chi tiết của một hệ thống nổi bật theo ID
// @Tags         Notable-Systems
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID hệ thống"
// @Success      200  {object}  utils.Response{data=dto.NotableSystemResponse}
// @Failure      400  {object}  utils.Response "Bad Request"
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response "Not Found"
// @Router       /notable-systems/{id} [get]
func (c *NotableSystemController) GetNotableSystemByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	system, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Notable system not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Notable system retrieved successfully", dto.FromNotableSystemModel(system))
}

// CreateNotableSystem godoc
// @Summary      Tạo hệ thống nổi bật mới
// @Description  Tạo một hệ thống nổi bật mới
// @Tags         Notable-Systems
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        system  body      dto.NotableSystemRequest  true  "Thông tin hệ thống"
// @Success      201     {object}  utils.Response{data=dto.NotableSystemResponse}
// @Failure      400     {object}  utils.Response "Bad Request"
// @Failure      401     {object}  utils.Response "Unauthorized"
// @Failure      500     {object}  utils.Response "Internal Server Error"
// @Router       /notable-systems [post]
func (c *NotableSystemController) CreateNotableSystem(ctx *gin.Context) {
	var request dto.NotableSystemRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	system := &models.NotableSystem{
		Name:        request.Name,
		Description: request.Description,
		StartYear:   request.StartYear,
		EndYear:     request.EndYear,
	}

	if err := c.repo.Create(ctx, system); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create notable system", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Notable system created successfully", dto.FromNotableSystemModel(system))
}

// UpdateNotableSystem godoc
// @Summary      Cập nhật hệ thống nổi bật
// @Description  Cập nhật thông tin của một hệ thống nổi bật
// @Tags         Notable-Systems
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id      path      int                      true  "ID hệ thống"
// @Param        system  body      dto.NotableSystemRequest  true  "Thông tin hệ thống"
// @Success      200     {object}  utils.Response{data=dto.NotableSystemResponse}
// @Failure      400     {object}  utils.Response "Bad Request"
// @Failure      401     {object}  utils.Response "Unauthorized"
// @Failure      404     {object}  utils.Response "Not Found"
// @Failure      500     {object}  utils.Response "Internal Server Error"
// @Router       /notable-systems/{id} [put]
func (c *NotableSystemController) UpdateNotableSystem(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	system, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Notable system not found", err.Error())
		return
	}

	var request dto.NotableSystemRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	system.Name = request.Name
	system.Description = request.Description
	system.StartYear = request.StartYear
	system.EndYear = request.EndYear

	if err := c.repo.Update(ctx, system); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update notable system", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Notable system updated successfully", dto.FromNotableSystemModel(system))
}

// DeleteNotableSystem godoc
// @Summary      Xóa hệ thống nổi bật
// @Description  Xóa một hệ thống nổi bật khỏi hệ thống
// @Tags         Notable-Systems
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID hệ thống"
// @Success      200  {object}  utils.Response "OK"
// @Failure      400  {object}  utils.Response "Bad Request"
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response "Internal Server Error"
// @Router       /notable-systems/{id} [delete]
func (c *NotableSystemController) DeleteNotableSystem(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	if err := c.repo.Delete(ctx, uint(id)); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete notable system", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Notable system deleted successfully", nil)
}
