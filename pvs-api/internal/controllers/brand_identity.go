package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BrandIdentityController struct {
	repo repositories.BrandIdentityRepository
}

func NewBrandIdentityController(db *gorm.DB) *BrandIdentityController {
	return &BrandIdentityController{
		repo: repositories.NewBrandIdentityRepository(db),
	}
}

// GetActiveBrandIdentity godoc
// @Summary      Lấy thông tin nhận diện thương hiệu hiện tại
// @Description  Trả về thông tin nhận diện thương hiệu đang có hiệu lực
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response{data=dto.BrandIdentityResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /brand-identities/active [get]
func (c *BrandIdentityController) GetActiveBrandIdentity(ctx *gin.Context) {
	identity, err := c.repo.GetActive(ctx)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "No active brand identity found", err.Error())
		return
	}

	response := dto.FromBrandIdentityModel(identity)
	utils.SuccessResponse(ctx, http.StatusOK, "Brand identity retrieved successfully", response)
}

// ListBrandIdentities godoc
// @Summary      Lấy lịch sử thay đổi nhận diện thương hiệu
// @Description  Trả về danh sách các lần thay đổi nhận diện thương hiệu
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /brand-identities [get]
func (c *BrandIdentityController) ListBrandIdentities(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	identities, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch brand identities", err.Error())
		return
	}

	var responses []*dto.BrandIdentityResponse
	for _, identity := range identities {
		responses = append(responses, dto.FromBrandIdentityModel(identity))
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Brand identities retrieved successfully", responses, total)
}

// GetBrandIdentityByID godoc
// @Summary      Lấy chi tiết một nhận diện thương hiệu
// @Description  Trả về thông tin chi tiết của một nhận diện thương hiệu theo ID
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID nhận diện thương hiệu"
// @Success      200  {object}  utils.Response{data=dto.BrandIdentityResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /brand-identities/{id} [get]
func (c *BrandIdentityController) GetBrandIdentityByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	identity, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Brand identity not found", err.Error())
		return
	}

	response := dto.FromBrandIdentityModel(identity)
	utils.SuccessResponse(ctx, http.StatusOK, "Brand identity retrieved successfully", response)
}

// CreateBrandIdentity godoc
// @Summary      Tạo nhận diện thương hiệu mới
// @Description  Tạo một nhận diện thương hiệu mới và tự động cập nhật thời gian hết hiệu lực của nhận diện cũ
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        identity  body      dto.BrandIdentityRequest  true  "Thông tin nhận diện thương hiệu"
// @Success      201  {object}  utils.Response{data=dto.BrandIdentityResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /brand-identities [post]
func (c *BrandIdentityController) CreateBrandIdentity(ctx *gin.Context) {
	var request dto.BrandIdentityRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	identity := &models.BrandIdentity{
		CompanyName: request.CompanyName,
		ColorCode:   request.ColorCode,
		LogoURL:     request.LogoURL,
		EffectiveAt: request.EffectiveAt,
		ExpiredAt:   request.ExpiredAt,
		Note:        request.Note,
	}

	if err := c.repo.Create(ctx, identity); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create brand identity", err.Error())
		return
	}

	response := dto.FromBrandIdentityModel(identity)
	utils.SuccessResponse(ctx, http.StatusCreated, "Brand identity created successfully", response)
}

// UpdateBrandIdentity godoc
// @Summary      Cập nhật nhận diện thương hiệu
// @Description  Cập nhật thông tin của một nhận diện thương hiệu
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id        path      int                     true  "ID nhận diện thương hiệu"
// @Param        identity  body      dto.BrandIdentityRequest  true  "Thông tin nhận diện thương hiệu"
// @Success      200  {object}  utils.Response{data=dto.BrandIdentityResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /brand-identities/{id} [put]
func (c *BrandIdentityController) UpdateBrandIdentity(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	identity, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Brand identity not found", err.Error())
		return
	}

	var request dto.BrandIdentityRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	identity.CompanyName = request.CompanyName
	identity.ColorCode = request.ColorCode
	identity.LogoURL = request.LogoURL
	identity.EffectiveAt = request.EffectiveAt
	identity.ExpiredAt = request.ExpiredAt
	identity.Note = request.Note

	if err := c.repo.Update(ctx, identity); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update brand identity", err.Error())
		return
	}

	response := dto.FromBrandIdentityModel(identity)
	utils.SuccessResponse(ctx, http.StatusOK, "Brand identity updated successfully", response)
}

// DeleteBrandIdentity godoc
// @Summary      Xóa nhận diện thương hiệu
// @Description  Xóa một nhận diện thương hiệu khỏi hệ thống
// @Tags         Brand-Identity
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID nhận diện thương hiệu"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /brand-identities/{id} [delete]
func (c *BrandIdentityController) DeleteBrandIdentity(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	if err := c.repo.Delete(ctx, uint(id)); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete brand identity", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Brand identity deleted successfully", nil)
}
