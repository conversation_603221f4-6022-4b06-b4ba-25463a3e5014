package controllers

import (
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type StakeholderController struct {
	repo repositories.StakeholderRepository
}

func NewStakeholderController(db *gorm.DB) *StakeholderController {
	return &StakeholderController{
		repo: repositories.NewStakeholderRepository(db),
	}
}

// GetAllStakeholders godoc
// @Summary      Lấy danh sách tất cả người phụ trách
// @Description  Trả về danh sách tất cả người phụ trách trong hệ thống
// @Tags         Stakeholders
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /stakeholders [get]
func (c *StakeholderController) GetAllStakeholders(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	stakeholders, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list stakeholders", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Stakeholders retrieved successfully", stakeholders, total)
}

// GetStakeholderByID godoc
// @Summary      Lấy thông tin người phụ trách theo ID
// @Description  Trả về thông tin chi tiết của một người phụ trách
// @Tags         Stakeholders
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID người phụ trách"
// @Success      200  {object}  dto.StakeholderResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /stakeholders/{id} [get]
func (c *StakeholderController) GetStakeholderByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	stakeholder, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stakeholder not found", err.Error())
		return
	}

	response := dto.StakeholderResponse{
		ID:        stakeholder.ID,
		FullName:  stakeholder.FullName,
		Email:     stakeholder.Email,
		CreatedAt: stakeholder.CreatedAt,
		UpdatedAt: stakeholder.UpdatedAt,
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Stakeholder retrieved successfully", response)
}

// CreateStakeholder godoc
// @Summary      Tạo người phụ trách mới
// @Description  Tạo một người phụ trách mới trong hệ thống
// @Tags         Stakeholders
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        stakeholder  body      dto.StakeholderRequest  true  "Thông tin người phụ trách"
// @Success      201  {object}  dto.StakeholderResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /stakeholders [post]
func (c *StakeholderController) CreateStakeholder(ctx *gin.Context) {
	var request dto.StakeholderRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem email đã tồn tại chưa
	existingStakeholder, _ := c.repo.GetByEmail(ctx, request.Email)
	if existingStakeholder != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Email already exists", "")
		return
	}

	stakeholder := models.Stakeholder{
		FullName: request.FullName,
		Email:    request.Email,
		Position: request.Position,
	}

	err := c.repo.Create(ctx, &stakeholder)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create stakeholder", err.Error())
		return
	}

	response := dto.StakeholderResponse{
		ID:        stakeholder.ID,
		FullName:  stakeholder.FullName,
		Email:     stakeholder.Email,
		CreatedAt: stakeholder.CreatedAt,
		UpdatedAt: stakeholder.UpdatedAt,
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Stakeholder created successfully", response)
}

// UpdateStakeholder godoc
// @Summary      Cập nhật thông tin người phụ trách
// @Description  Cập nhật thông tin của một người phụ trách
// @Tags         Stakeholders
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id          path      int                     true  "ID người phụ trách"
// @Param        stakeholder  body      dto.StakeholderRequest  true  "Thông tin người phụ trách"
// @Success      200  {object}  dto.StakeholderResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /stakeholders/{id} [put]
func (c *StakeholderController) UpdateStakeholder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem stakeholder có tồn tại không
	existingStakeholder, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stakeholder not found", err.Error())
		return
	}

	var request dto.StakeholderRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem email mới đã tồn tại chưa (nếu email thay đổi)
	if request.Email != existingStakeholder.Email {
		stakeholderWithEmail, _ := c.repo.GetByEmail(ctx, request.Email)
		if stakeholderWithEmail != nil {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Email already exists", "")
			return
		}
	}

	// Cập nhật thông tin
	existingStakeholder.FullName = request.FullName
	existingStakeholder.Email = request.Email
	existingStakeholder.Position = request.Position

	err = c.repo.Update(ctx, existingStakeholder)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update stakeholder", err.Error())
		return
	}

	response := dto.StakeholderResponse{
		ID:        existingStakeholder.ID,
		FullName:  existingStakeholder.FullName,
		Email:     existingStakeholder.Email,
		CreatedAt: existingStakeholder.CreatedAt,
		UpdatedAt: existingStakeholder.UpdatedAt,
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Stakeholder updated successfully", response)
}

// DeleteStakeholder godoc
// @Summary      Xóa người phụ trách
// @Description  Xóa một người phụ trách khỏi hệ thống
// @Tags         Stakeholders
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID người phụ trách"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /stakeholders/{id} [delete]
func (c *StakeholderController) DeleteStakeholder(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem stakeholder có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stakeholder not found", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete stakeholder", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Stakeholder deleted successfully", nil)
}
