package controllers

import (
	"net/http"
	"pvs-api/internal/dto"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AuthController struct {
	userRepo  repositories.UserRepository
	roleRepo  repositories.RoleRepository
	authRepo  repositories.AuthRepository
	jwtSecret string
}

func NewAuthController(db *gorm.DB, jwtSecret string) *AuthController {
	return &AuthController{
		userRepo:  repositories.NewUserRepository(db),
		roleRepo:  repositories.NewRoleRepository(db),
		authRepo:  repositories.NewAuthRepository(),
		jwtSecret: jwtSecret,
	}
}

// Login godoc
// @Summary Đăng nhập vào hệ thống
// @Description Xác thực người dùng và trả về JWT token
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body dto.LoginRequest true "Thông tin đăng nhập"
// @Success 200 {object} utils.Response{data=dto.AuthResponse} "Đăng nhập thành công"
// @Failure 400 {object} utils.Response "Lỗi dữ liệu đầu vào"
// @Failure 401 {object} utils.Response "Thông tin đăng nhập không hợp lệ"
// @Failure 500 {object} utils.Response "Lỗi server"
// @Router /login [post]
func (c *AuthController) Login(ctx *gin.Context) {
	var login dto.LoginRequest
	if err := ctx.ShouldBindJSON(&login); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid input data", err.Error())
		return
	}

	user, err := c.userRepo.GetByUsername(ctx, login.Username)
	if err != nil || !user.CheckPassword(login.Password) {
		utils.ErrorResponse(ctx, http.StatusUnauthorized, "Authentication failed", "Invalid credentials")
		return
	}

	// Lấy permissions từ database
	permissions, err := c.roleRepo.ListPermissionsByRole(ctx, user.RoleCode)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Internal server error", "Failed to retrieve permissions")
		return
	}

	// Tạo JWT token với permissions
	token, err := c.authRepo.GenerateToken(
		user.ID,
		user.Username,
		user.RoleCode,
		permissions,
		c.jwtSecret,
	)

	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Internal server error", "Failed to generate token")
		return
	}

	response := dto.AuthResponse{
		Token: token,
		User: dto.UserInfo{
			ID:          user.ID,
			Username:    user.Username,
			RoleCode:    user.RoleCode,
			Permissions: permissions,
		},
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Login successful", response)
}

// RefreshToken godoc
// @Summary Làm mới token
// @Description Tạo token mới dựa trên token hiện tại
// @Tags Authentication
// @Accept json
// @Produce json
// @Param request body dto.RefreshTokenRequest true "Token hiện tại"
// @Success 200 {object} utils.Response{data=dto.AuthResponse} "Token mới"
// @Failure 400 {object} utils.Response "Lỗi dữ liệu đầu vào"
// @Failure 401 {object} utils.Response "Token không hợp lệ hoặc người dùng không tồn tại"
// @Failure 500 {object} utils.Response "Lỗi server"
// @Router /refresh-token [post]
func (c *AuthController) RefreshToken(ctx *gin.Context) {
	var request dto.RefreshTokenRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid input data", err.Error())
		return
	}

	// Validate token hiện tại
	claims, err := c.authRepo.ValidateToken(request.Token, c.jwtSecret)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusUnauthorized, "Authentication failed", "Invalid token")
		return
	}

	// Lấy thông tin user từ database để đảm bảo user vẫn tồn tại và active
	user, err := c.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusUnauthorized, "Authentication failed", "User not found")
		return
	}

	if !user.IsActive {
		utils.ErrorResponse(ctx, http.StatusUnauthorized, "Authentication failed", "User is inactive")
		return
	}

	// Lấy permissions mới nhất từ database
	permissions, err := c.roleRepo.ListPermissionsByRole(ctx, user.RoleCode)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Internal server error", "Failed to retrieve permissions")
		return
	}

	// Tạo token mới
	newToken, err := c.authRepo.GenerateToken(
		user.ID,
		user.Username,
		user.RoleCode,
		permissions,
		c.jwtSecret,
	)

	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Internal server error", "Failed to generate token")
		return
	}

	response := dto.AuthResponse{
		Token: newToken,
		User: dto.UserInfo{
			ID:          user.ID,
			Username:    user.Username,
			RoleCode:    user.RoleCode,
			Permissions: permissions,
		},
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Token refreshed successfully", response)
}
