package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductJourneyController struct {
	repo          repositories.ProductJourneyRepository
	productRepo   repositories.ProductRepository
	componentRepo repositories.ComponentRepository
}

func NewProductJourneyController(db *gorm.DB) *ProductJourneyController {
	return &ProductJourneyController{
		repo:          repositories.NewProductJourneyRepository(db),
		productRepo:   repositories.NewProductRepository(db),
		componentRepo: repositories.NewComponentRepository(db),
	}
}

// ListProductJourneys godoc
// @Summary      Lấy danh sách hành trình sản phẩm
// @Description  Trả về danh sách hành trình sản phẩm với phân trang và filter
// @Tags         Product Journeys
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page         query     int     false  "Số trang"
// @Param        limit        query     int     false  "Số lượng mỗi trang"
// @Param        product_id   query     int     false  "ID sản phẩm"
// @Param        status       query     string  false  "Trạng thái"
// @Param        search       query     string  false  "Tìm kiếm theo tên hoặc mô tả"
// @Success      200  {object}  utils.Response{data=[]dto.ProductJourneyListResponse}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-journeys [get]
func (c *ProductJourneyController) ListProductJourneys(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	var filters repositories.ProductJourneyFilters
	if err := ctx.ShouldBindQuery(&filters); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	journeys, total, err := c.repo.List(ctx, params, &filters)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product journeys", err.Error())
		return
	}

	var responses []dto.ProductJourneyListResponse
	for _, journey := range journeys {
		responses = append(responses, *dto.FromProductJourneyModelList(journey))
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Product journeys retrieved successfully", responses, total)
}

// GetProductJourneyByID godoc
// @Summary      Lấy thông tin chi tiết hành trình sản phẩm
// @Description  Trả về thông tin chi tiết của một hành trình sản phẩm
// @Tags         Product Journeys
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID hành trình"
// @Success      200  {object}  utils.Response{data=dto.ProductJourneyResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-journeys/{id} [get]
func (c *ProductJourneyController) GetProductJourneyByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid journey ID", err.Error())
		return
	}

	journey, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(ctx, http.StatusNotFound, "Product journey not found", "")
			return
		}
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product journey", err.Error())
		return
	}

	response := dto.FromProductJourneyModel(journey)
	utils.SuccessResponse(ctx, http.StatusOK, "Product journey retrieved successfully", response)
}

// CreateProductJourney godoc
// @Summary      Tạo hành trình sản phẩm mới
// @Description  Tạo một hành trình sản phẩm mới với thông tin được cung cấp
// @Tags         Product Journeys
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        journey  body      dto.ProductJourneyRequest  true  "Thông tin hành trình"
// @Success      201  {object}  utils.Response{data=dto.ProductJourneyResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-journeys [post]
func (c *ProductJourneyController) CreateProductJourney(ctx *gin.Context) {
	var request dto.ProductJourneyRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Validate product exists
	_, err := c.productRepo.GetByID(ctx, request.ProductID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Product not found", "")
			return
		}
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to validate product", err.Error())
		return
	}

	// Convert DTO to model
	journey := request.ToProductJourneyModel()

	// Load components if provided
	if len(request.Components) > 0 {
		for _, compReq := range request.Components {
			// Verify component exists
			_, err := c.componentRepo.GetByID(ctx, compReq.ComponentID)
			if err != nil {
				utils.ErrorResponse(ctx, http.StatusBadRequest, "Component not found", fmt.Sprintf("Component ID %d not found", compReq.ComponentID))
				return
			}

			component := &models.Component{ID: compReq.ComponentID}
			journey.Components = append(journey.Components, component)
		}
	}

	err = c.repo.Create(ctx, journey)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product journey", err.Error())
		return
	}

	// Fetch the created journey with relationships
	createdJourney, err := c.repo.GetByID(ctx, journey.ID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch created journey", err.Error())
		return
	}

	response := dto.FromProductJourneyModel(createdJourney)
	utils.SuccessResponse(ctx, http.StatusCreated, "Product journey created successfully", response)
}

// UpdateProductJourney godoc
// @Summary      Cập nhật hành trình sản phẩm
// @Description  Cập nhật thông tin của một hành trình sản phẩm
// @Tags         Product Journeys
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id       path      int                        true  "ID hành trình"
// @Param        journey  body      dto.ProductJourneyRequest  true  "Thông tin hành trình"
// @Success      200  {object}  utils.Response{data=dto.ProductJourneyResponse}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-journeys/{id} [put]
func (c *ProductJourneyController) UpdateProductJourney(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid journey ID", err.Error())
		return
	}

	var request dto.ProductJourneyRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Check if journey exists
	existingJourney, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(ctx, http.StatusNotFound, "Product journey not found", "")
			return
		}
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch journey", err.Error())
		return
	}

	// Update journey fields
	existingJourney.Name = request.Name
	existingJourney.Description = request.Description
	existingJourney.ProductID = request.ProductID
	existingJourney.FlowData = request.FlowData
	existingJourney.Status = request.Status

	// Reset components for update
	existingJourney.Components = nil

	// Load components if provided
	existingJourney.Components = nil // Clear existing components
	if len(request.Components) > 0 {
		for _, compReq := range request.Components {
			// Verify component exists
			_, err := c.componentRepo.GetByID(ctx, compReq.ComponentID)
			if err != nil {
				utils.ErrorResponse(ctx, http.StatusBadRequest, "Component not found", fmt.Sprintf("Component ID %d not found", compReq.ComponentID))
				return
			}

			component := &models.Component{ID: compReq.ComponentID}
			existingJourney.Components = append(existingJourney.Components, component)
		}
	}

	err = c.repo.Update(ctx, existingJourney)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product journey", err.Error())
		return
	}

	// Fetch the updated journey with relationships
	updatedJourney, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch updated journey", err.Error())
		return
	}

	response := dto.FromProductJourneyModel(updatedJourney)
	utils.SuccessResponse(ctx, http.StatusOK, "Product journey updated successfully", response)
}

// DeleteProductJourney godoc
// @Summary      Xóa hành trình sản phẩm
// @Description  Xóa một hành trình sản phẩm khỏi hệ thống
// @Tags         Product Journeys
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID hành trình"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-journeys/{id} [delete]
func (c *ProductJourneyController) DeleteProductJourney(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid journey ID", err.Error())
		return
	}

	// Check if journey exists
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(ctx, http.StatusNotFound, "Product journey not found", "")
			return
		}
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch journey", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete product journey", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product journey deleted successfully", nil)
}
