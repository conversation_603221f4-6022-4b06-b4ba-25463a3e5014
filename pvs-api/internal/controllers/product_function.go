package controllers

import (
    "net/http"
    "pvs-api/internal/dto"
    "pvs-api/internal/repositories"
    "pvs-api/pkg/utils"
    "strconv"

    "github.com/gin-gonic/gin"
)

type ProductFunctionController struct {
    repo repositories.ProductFunctionRepository
}

func NewProductFunctionController(repo repositories.ProductFunctionRepository) *ProductFunctionController {
    return &ProductFunctionController{
        repo: repo,
    }
}

// ListProductFunctions godoc
// @Summary      Lấy danh sách chức năng của sản phẩm
// @Description  Trả về danh sách các chức năng của sản phẩm
// @Tags         Product-Functions
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        product_id  query   integer  false  "ID của sản phẩm cần lọc"
// @Param        page        query   integer  false  "Số trang"
// @Param        limit       query   integer  false  "Số lượng mỗi trang"
// @Success      200  {object}  utils.Response{data=[]dto.ProductFunctionResponse}
// @Failure      400  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-functions [get]
func (c *ProductFunctionController) ListProductFunctions(ctx *gin.Context) {
    params := utils.NewPagination(ctx)
    filters := make(map[string]interface{})

    // Parse product_id filter
    if productIDStr := ctx.Query("product_id"); productIDStr != "" {
        productID, err := strconv.ParseUint(productIDStr, 10, 32)
        if err != nil {
            utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
            return
        }
        filters["product_id"] = uint(productID)
    }

    functions, total, err := c.repo.ListProductFunctions(ctx, filters, params)
    if err != nil {
        utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product functions", err.Error())
        return
    }

    utils.SuccessPaggingResponse(ctx, http.StatusOK, "Product functions retrieved successfully", functions, total)
}

// GetProductFunctionByID godoc
// @Summary      Lấy thông tin chi tiết của một chức năng
// @Description  Trả về thông tin chi tiết của một chức năng theo ID
// @Tags         Product-Functions
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      integer  true  "ID của chức năng"
// @Success      200  {object}  utils.Response{data=dto.ProductFunctionResponse}
// @Failure      400  {object}  utils.Response
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-functions/{id} [get]
func (c *ProductFunctionController) GetProductFunctionByID(ctx *gin.Context) {
    id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
    if err != nil {
        utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
        return
    }

    function, err := c.repo.GetProductFunctionByID(ctx, uint(id))
    if err != nil {
        utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product function", err.Error())
        return
    }

    if function == nil {
        utils.ErrorResponse(ctx, http.StatusNotFound, "Product function not found", "")
        return
    }

    utils.SuccessResponse(ctx, http.StatusOK, "Product function retrieved successfully", function)
}

// CreateProductFunction godoc
// @Summary      Tạo mới một chức năng
// @Description  Tạo mới một chức năng cho sản phẩm
// @Tags         Product-Functions
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        request  body      dto.ProductFunctionRequest  true  "Thông tin chức năng"
// @Success      201      {object}  utils.Response{data=dto.ProductFunctionResponse}
// @Failure      400      {object}  utils.Response
// @Failure      500      {object}  utils.Response
// @Router       /product-functions [post]
func (c *ProductFunctionController) CreateProductFunction(ctx *gin.Context) {
    var request dto.ProductFunctionRequest
    if err := ctx.ShouldBindJSON(&request); err != nil {
        utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
        return
    }

    function, err := c.repo.CreateProductFunction(ctx, &request)
    if err != nil {
        utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product function", err.Error())
        return
    }

    utils.SuccessResponse(ctx, http.StatusCreated, "Product function created successfully", function)
}

// UpdateProductFunction godoc
// @Summary      Cập nhật thông tin chức năng
// @Description  Cập nhật thông tin của một chức năng
// @Tags         Product-Functions
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id       path      integer                    true  "ID của chức năng"
// @Param        request  body