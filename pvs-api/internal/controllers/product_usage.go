package controllers

import (
	"net/http"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductUsageController struct {
	repo repositories.ProductUsageRepository
}

func NewProductUsageController(db *gorm.DB) *ProductUsageController {
	return &ProductUsageController{
		repo: repositories.NewProductUsageRepository(db),
	}
}

// UpdateUsageStat godoc
// @Summary      Cập nhật số liệu người dùng sản phẩm
// @Description  Cập nhật số lượng người dùng cho một sản phẩm trong tháng
// @Tags         Product-Usage
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        request body models.ProductUsageStat true "Thông tin số liệu"
// @Success      200  {object}  utils.Response{data=models.ProductUsageStat}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-usage/stats [post]
func (c *ProductUsageController) UpdateUsageStat(ctx *gin.Context) {
	var stat models.ProductUsageStat
	if err := ctx.ShouldBindJSON(&stat); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	if err := c.repo.UpsertStat(ctx, &stat); err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update usage stat", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Usage stat updated successfully", stat)
}

// GetProductStats godoc
// @Summary      Lấy số liệu người dùng theo sản phẩm
// @Description  Lấy số liệu người dùng của một sản phẩm trong khoảng thời gian
// @Tags         Product-Usage
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        product_id  path      int  true  "ID sản phẩm"
// @Param        start_date query     string  true  "Ngày bắt đầu (YYYY-MM-DD)"
// @Param        end_date   query     string  true  "Ngày kết thúc (YYYY-MM-DD)"
// @Success      200  {array}   utils.Response{data=[]models.ProductUsageStat}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-usage/products/{product_id}/stats [get]
func (c *ProductUsageController) GetProductStats(ctx *gin.Context) {
	productID := ctx.Param("product_id")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid start date format", err.Error())
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid end date format", err.Error())
		return
	}

	productIDUint, err := strconv.ParseUint(productID, 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID", err.Error())
		return
	}
	stats, err := c.repo.GetStatsByProduct(ctx, uint(productIDUint), start, end)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product stats", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stats retrieved successfully", stats)
}

// GetMonthlyStats godoc
// @Summary      Lấy số liệu người dùng theo tháng
// @Description  Lấy số liệu người dùng của tất cả sản phẩm trong một tháng
// @Tags         Product-Usage
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        month query     string  true  "Tháng (YYYY-MM)"
// @Success      200  {array}   utils.Response{data=[]models.ProductUsageStat}
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-usage/monthly-stats [get]
func (c *ProductUsageController) GetMonthlyStats(ctx *gin.Context) {
	monthStr := ctx.Query("month")
	month, err := time.Parse("2006-01", monthStr)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid month format", err.Error())
		return
	}

	stats, err := c.repo.GetStatsByMonth(ctx, month)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product stats", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stats retrieved successfully", stats)
}

// GetMultiProductStats godoc
// @Summary      Lấy số liệu nhiều sản phẩm theo thời gian
// @Description  Lấy số liệu người dùng của nhiều sản phẩm trong khoảng thời gian
// @Tags         Product-Usage
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        product_ids query    string  true  "Danh sách ID sản phẩm, phân cách bởi dấu phẩy (vd: 1,2,3)"
// @Param        start_date  query    string  true  "Ngày bắt đầu (YYYY-MM-DD)"
// @Param        end_date    query    string  true  "Ngày kết thúc (YYYY-MM-DD)"
// @Success      200  {object}  MultiProductStatsResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-usage/multi-products/stats [get]
func (c *ProductUsageController) GetMultiProductStats(ctx *gin.Context) {
	// Validate và parse product IDs
	productIDsStr := ctx.QueryArray("product_ids")
	if len(productIDsStr) == 0 {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Product IDs are required", nil)
		return
	}

	var productIDs []uint
	for _, idStr := range productIDsStr {
		id, err := strconv.ParseUint(idStr, 10, 32)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product ID format", err.Error())
			return
		}
		productIDs = append(productIDs, uint(id))
	}

	// Parse start date
	startDate, err := time.Parse("2006-01-02", ctx.Query("start_date"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid start date format", err.Error())
		return
	}

	// Parse end date
	endDate, err := time.Parse("2006-01-02", ctx.Query("end_date"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid end date format", err.Error())
		return
	}

	// Lấy dữ liệu từ repository
	stats, err := c.repo.GetStatsMultiProducts(ctx, productIDs, startDate, endDate)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product stats", err.Error())
		return
	}

	// Tổ chức dữ liệu theo format phù hợp cho biểu đồ
	response := formatMultiProductStats(stats)
	utils.SuccessResponse(ctx, http.StatusOK, "Product stats retrieved successfully", response)
}

type MultiProductStatsResponse struct {
	Labels   []string         `json:"labels"`   // Các mốc thời gian
	Datasets []ProductDataset `json:"datasets"` // Dữ liệu của từng sản phẩm
}

type ProductDataset struct {
	ProductID   uint   `json:"product_id"`
	ProductName string `json:"product_name"`
	Data        []int  `json:"data"` // Số liệu theo thời gian
}

func formatMultiProductStats(stats []models.ProductUsageStat) MultiProductStatsResponse {
	// Map để lưu trữ dữ liệu tạm thời
	timeMap := make(map[string]bool)
	productMap := make(map[uint]map[string]int)
	productNames := make(map[uint]string)

	// Tạo map dữ liệu
	for _, stat := range stats {
		monthStr := stat.Month.Format("2006-01")
		timeMap[monthStr] = true

		if productMap[stat.ProductID] == nil {
			productMap[stat.ProductID] = make(map[string]int)
			productNames[stat.ProductID] = stat.Product.Name
		}
		productMap[stat.ProductID][monthStr] = stat.Users
	}

	// Tạo mảng labels (thời gian) đã sắp xếp
	var labels []string
	for timeStr := range timeMap {
		labels = append(labels, timeStr)
	}
	sort.Strings(labels)

	// Tạo datasets cho từng sản phẩm
	var datasets []ProductDataset
	for productID, dataMap := range productMap {
		dataset := ProductDataset{
			ProductID:   productID,
			ProductName: productNames[productID],
			Data:        make([]int, len(labels)),
		}

		// Điền dữ liệu theo thứ tự của labels
		for i, label := range labels {
			dataset.Data[i] = dataMap[label]
		}

		datasets = append(datasets, dataset)
	}

	// Sắp xếp datasets theo ProductID
	sort.Slice(datasets, func(i, j int) bool {
		return datasets[i].ProductID < datasets[j].ProductID
	})

	return MultiProductStatsResponse{
		Labels:   labels,
		Datasets: datasets,
	}
}

// GetAllProductStats godoc
// @Summary      Lấy số liệu tất cả sản phẩm theo thời gian
// @Description  Lấy số liệu người dùng của tất cả sản phẩm trong khoảng thời gian
// @Tags         Product-Usage
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        start_month  query    string  true  "Tháng bắt đầu (YYYY-MM)"
// @Param        end_month    query    string  true  "Tháng kết thúc (YYYY-MM)"
// @Success      200  {object}  MultiProductStatsResponse
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-usage/all-products/stats [get]
func (c *ProductUsageController) GetAllProductStats(ctx *gin.Context) {
	// Parse months
	startMonth, err := time.Parse("2006-01", ctx.Query("start_month"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid start month format. Expected YYYY-MM", err.Error())
		return
	}

	endMonth, err := time.Parse("2006-01", ctx.Query("end_month"))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid end month format. Expected YYYY-MM", err.Error())
		return
	}

	// Lấy dữ liệu từ repository
	stats, err := c.repo.GetStatsAllProducts(ctx, startMonth, endMonth)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to fetch product stats", err.Error())
		return
	}

	// Sử dụng lại hàm formatMultiProductStats từ API trước
	response := formatMultiProductStats(stats)
	utils.SuccessResponse(ctx, http.StatusOK, "Product stats retrieved successfully", response)
}
