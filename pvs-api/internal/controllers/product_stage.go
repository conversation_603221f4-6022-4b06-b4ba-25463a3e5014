package controllers

import (
	"errors"
	"net/http"
	"strconv"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ProductStageController struct {
	repo            repositories.ProductStageRepository
	productRepo     repositories.ProductRepository
	stakeholderRepo repositories.StakeholderRepository
}

func NewProductStageController(db *gorm.DB) *ProductStageController {
	return &ProductStageController{
		repo:            repositories.NewProductStageRepository(db),
		productRepo:     repositories.NewProductRepository(db),
		stakeholderRepo: repositories.NewStakeholderRepository(db),
	}
}

// GetAllProductStages godoc
// @Summary      Lấy danh sách tất cả giai đo<PERSON>n sản phẩm
// @Description  Trả về danh sách tất cả giai đoạn sản phẩm trong hệ thống
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200  {object}  utils.Response{data=[]models.ProductStage}
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-stages [get]
func (c *ProductStageController) GetAllProductStages(ctx *gin.Context) {
	params := utils.NewPagination(ctx)

	stages, total, err := c.repo.List(ctx, params)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to list product stages", err.Error())
		return
	}

	utils.SuccessPaggingResponse(ctx, http.StatusOK, "Product stages retrieved successfully", stages, total)
}

// GetProductStageByID godoc
// @Summary      Lấy thông tin giai đoạn sản phẩm theo ID
// @Description  Trả về thông tin chi tiết của một giai đoạn sản phẩm
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID giai đoạn sản phẩm"
// @Success      200  {object}  models.ProductStage
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Router       /product-stages/{id} [get]
func (c *ProductStageController) GetProductStageByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	stage, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stage retrieved successfully", stage)
}

// CreateProductStage godoc
// @Summary      Tạo giai đoạn sản phẩm mới
// @Description  Tạo một giai đoạn sản phẩm mới trong hệ thống
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        productStage  body      dto.ProductStageRequest  true  "Thông tin giai đoạn sản phẩm"
// @Success      201  {object}  models.ProductStage
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      500  {object}  utils.Response
// @Router       /product-stages [post]
func (c *ProductStageController) CreateProductStage(ctx *gin.Context) {
	var request dto.ProductStageRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem sản phẩm có tồn tại không
	exists, err := c.productRepo.ExistByID(ctx, request.ProductID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product stage", err.Error())
		return
	}

	if !exists {
		err = errors.New("product not found")
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
		return
	}

	stage := models.ProductStage{
		ProductID: request.ProductID,
		StageCode: request.StageCode,
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		Notes:     request.Notes,
	}

	err = c.repo.Create(ctx, &stage)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to create product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusCreated, "Product stage created successfully", stage)
}

// UpdateProductStage godoc
// @Summary      Cập nhật thông tin giai đoạn sản phẩm
// @Description  Cập nhật thông tin của một giai đoạn sản phẩm
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int                      true  "ID giai đoạn sản phẩm"
// @Param        productStage  body      dto.ProductStageRequest  true  "Thông tin giai đoạn sản phẩm"
// @Success      200  {object}  models.ProductStage
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-stages/{id} [put]
func (c *ProductStageController) UpdateProductStage(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem giai đoạn sản phẩm có tồn tại không
	existingStage, err := c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	var request dto.ProductStageRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem sản phẩm có tồn tại không (nếu thay đổi)
	if request.ProductID != existingStage.ProductID {
		exists, err := c.productRepo.ExistByID(ctx, request.ProductID)
		if err != nil {
			utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product stage", err.Error())
			return
		}

		if !exists {
			err = errors.New("product not found")
			utils.ErrorResponse(ctx, http.StatusNotFound, "Product not found", err.Error())
			return
		}
	}

	// Cập nhật thông tin
	existingStage.ProductID = request.ProductID
	existingStage.StageCode = request.StageCode
	existingStage.StartDate = request.StartDate
	existingStage.EndDate = request.EndDate
	existingStage.Notes = request.Notes

	err = c.repo.Update(ctx, existingStage)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to update product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stage updated successfully", existingStage)
}

// DeleteProductStage godoc
// @Summary      Xóa giai đoạn sản phẩm
// @Description  Xóa một giai đoạn sản phẩm khỏi hệ thống
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID giai đoạn sản phẩm"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-stages/{id} [delete]
func (c *ProductStageController) DeleteProductStage(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem giai đoạn sản phẩm có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	err = c.repo.Delete(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to delete product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stage deleted successfully", nil)
}

// GetProductStageWithStakeholders godoc
// @Summary      Lấy danh sách người phụ trách của giai đoạn sản phẩm
// @Description  Trả về danh sách các người phụ trách tham gia vào giai đoạn sản phẩm
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id   path      int  true  "ID giai đoạn sản phẩm"
// @Success      200  {array}   models.ProductStageStakeholder
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-stages/{id}/stakeholders [get]
func (c *ProductStageController) GetProductStageWithStakeholders(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem giai đoạn sản phẩm có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	stakeholders, err := c.repo.GetStakeholders(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to get product stage stakeholders", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Product stage stakeholders retrieved successfully", stakeholders)
}

// AddStakeholderToProductStage godoc
// @Summary      Thêm người phụ trách vào giai đoạn sản phẩm
// @Description  Thêm một người phụ trách vào giai đoạn sản phẩm
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id           path      int                      true  "ID giai đoạn sản phẩm"
// @Param        stakeholder  body      dto.ProductStageStakeholderRequest  true  "Thông tin người phụ trách"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-stages/{id}/stakeholders [post]
func (c *ProductStageController) AddStakeholderToProductStage(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid ID", err.Error())
		return
	}

	// Kiểm tra xem giai đoạn sản phẩm có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	var request dto.ProductStageStakeholderRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	// Kiểm tra xem stakeholder có tồn tại không
	_, err = c.stakeholderRepo.GetByID(ctx, request.StakeholderID)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stakeholder not found", err.Error())
		return
	}

	err = c.repo.AddStakeholder(ctx, uint(id), request.StakeholderID, request.RoleCode, request.Tasks)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to add stakeholder to product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Stakeholder added to product stage successfully", nil)
}

// RemoveStakeholderFromProductStage godoc
// @Summary      Xóa người phụ trách khỏi giai đoạn sản phẩm
// @Description  Xóa một người phụ trách khỏi giai đoạn sản phẩm
// @Tags         Product-Stages
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id             path      int  true  "ID giai đoạn sản phẩm"
// @Param        stakeholder_id  path      int  true  "ID người phụ trách"
// @Success      200  {object}  utils.Response
// @Failure      400  {object}  utils.Response
// @Failure      401  {object}  utils.Response "Unauthorized"
// @Failure      404  {object}  utils.Response
// @Failure      500  {object}  utils.Response
// @Router       /product-stages/{id}/stakeholders/{stakeholder_id} [delete]
func (c *ProductStageController) RemoveStakeholderFromProductStage(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid product stage ID", err.Error())
		return
	}

	stakeholderID, err := strconv.ParseUint(ctx.Param("stakeholder_id"), 10, 64)
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusBadRequest, "Invalid stakeholder ID", err.Error())
		return
	}

	// Kiểm tra xem giai đoạn sản phẩm có tồn tại không
	_, err = c.repo.GetByID(ctx, uint(id))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Product stage not found", err.Error())
		return
	}

	// Kiểm tra xem stakeholder có tồn tại không
	_, err = c.stakeholderRepo.GetByID(ctx, uint(stakeholderID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusNotFound, "Stakeholder not found", err.Error())
		return
	}

	err = c.repo.RemoveStakeholder(ctx, uint(id), uint(stakeholderID))
	if err != nil {
		utils.ErrorResponse(ctx, http.StatusInternalServerError, "Failed to remove stakeholder from product stage", err.Error())
		return
	}

	utils.SuccessResponse(ctx, http.StatusOK, "Stakeholder removed from product stage successfully", nil)
}
