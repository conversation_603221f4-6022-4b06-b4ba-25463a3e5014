package dto

import (
	"time"
)

// ProductStageRequest định nghĩa DTO cho việc tạo/cập nhật ProductStage
type ProductStageRequest struct {
	ProductID uint       `json:"product_id" binding:"required" example:"1"`
	StageCode string     `json:"stage_code" binding:"required" example:"IDEA"`
	StartDate time.Time  `json:"start_date" binding:"required" example:"2023-01-01T00:00:00Z"`
	EndDate   *time.Time `json:"end_date" example:"2023-02-01T00:00:00Z"`
	Notes     string     `json:"notes" example:"Hoàn thành giai đoạn nghiên cứu ý tưởng"`
}

// ProductStageResponse định nghĩa DTO cho phản hồi khi truy vấn ProductStage
type ProductStageResponse struct {
	ID        uint       `json:"id" example:"1"`
	ProductID uint       `json:"product_id" example:"1"`
	StageCode string     `json:"stage_code" example:"IDEA"`
	StartDate time.Time  `json:"start_date" example:"2023-01-01T00:00:00Z"`
	EndDate   *time.Time `json:"end_date" example:"2023-02-01T00:00:00Z"`
	Notes     string     `json:"notes" example:"Hoàn thành giai đoạn nghiên cứu ý tưởng"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// ProductStageDetailResponse định nghĩa DTO cho phản hồi chi tiết ProductStage
type ProductStageDetailResponse struct {
	ID           uint                  `json:"id" example:"1"`
	ProductID    uint                  `json:"product_id" example:"1"`
	Product      *ProductResponse      `json:"product,omitempty"`
	StageCode    string                `json:"stage_code,omitempty"`
	StartDate    time.Time             `json:"start_date" example:"2023-01-01T00:00:00Z"`
	EndDate      *time.Time            `json:"end_date" example:"2023-02-01T00:00:00Z"`
	Notes        string                `json:"notes" example:"Hoàn thành giai đoạn nghiên cứu ý tưởng"`
	Stakeholders []StakeholderWithRole `json:"stakeholders,omitempty"`
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
}

// StakeholderWithRole định nghĩa DTO cho Stakeholder với thông tin vai trò
type StakeholderWithRole struct {
	StakeholderResponse
	RoleCode string `json:"role_code" example:"PRIMARY"`
	Tasks    string `json:"tasks,omitempty" example:"Phụ trách quản lý tiến độ sprint"`
}

type ProductStageStakeholderRequest struct {
	StakeholderID uint   `json:"stakeholder_id" binding:"required"`
	RoleCode      string `json:"role_code" binding:"required" example:"PRIMARY"`
	Tasks         string `json:"tasks,omitempty" example:"Phụ trách quản lý tiến độ sprint"`
}
