package dto

import "pvs-api/internal/models"

type RoleRequest struct {
	Code        string   `json:"code" binding:"required" example:"PRODUCT_MANAGER"`
	Name        string   `json:"name" binding:"required" example:"Product Manager"`
	Description string   `json:"description" example:"Manages product lifecycle"`
	Permissions []string `json:"permissions" example:"['product:create','product:update']"`
}

type RoleResponse struct {
	Code        string   `json:"code" example:"PRODUCT_MANAGER"`
	Name        string   `json:"name" example:"Product Manager"`
	Description string   `json:"description" example:"Manages product lifecycle"`
	IsSystem    bool     `json:"is_system" example:"false"`
	Permissions []string `json:"permissions,omitempty" example:"['product:create','product:update']"`
}

type UpdateRolePermissionsRequest struct {
	Permissions []string `json:"permissions" binding:"required"`
}

func FromRoleModel(role *models.Role) *RoleResponse {
	return &RoleResponse{
		Code:        role.Code,
		Name:        role.Name,
		Description: role.Description,
		IsSystem:    role.IsSystem,
		Permissions: role.Permissions,
	}
}
