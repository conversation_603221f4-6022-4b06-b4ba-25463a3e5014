package dto

// ProductFunctionRequest represents the request to create/update a product function
type ProductFunctionRequest struct {
    ProductID   uint   `json:"product_id" binding:"required"`
    Name        string `json:"name" binding:"required"`
    Description string `json:"description"`
}

// ProductFunctionResponse represents the response for a product function
type ProductFunctionResponse struct {
    ID          uint   `json:"id"`
    ProductID   uint   `json:"product_id"`
    ProductName string `json:"product_name,omitempty"`
    Name        string `json:"name"`
    Description string `json:"description"`
    FeatureCount int   `json:"feature_count,omitempty"`
}

// FeatureRequest represents the request to create/update a feature
type FeatureRequest struct {
    FunctionID  uint   `json:"function_id" binding:"required"`
    Name        string `json:"name" binding:"required"`
    Description string `json:"description"`
    Status      string `json:"status"`
}

// FeatureResponse represents the response for a feature
type FeatureResponse struct {
    ID          uint   `json:"id"`
    FunctionID  uint   `json:"function_id"`
    FunctionName string `json:"function_name,omitempty"`
    Name        string `json:"name"`
    Description string `json:"description"`
    Status      string `json:"status"`
    BusinessFlowCount int `json:"business_flow_count,omitempty"`
}