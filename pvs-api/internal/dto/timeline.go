package dto

type TimelineComponentInfo struct {
	Name string `json:"name"`
	Link string `json:"link,omitempty"`
}

type TimelineStatusLog struct {
	Status    string `json:"status"`
	StartYear int    `json:"start_year"`
	EndYear   *int   `json:"end_year,omitempty"`
}

type TimelineStage struct {
	Name      string `json:"name"`
	StartYear int    `json:"start_year"`
	EndYear   *int   `json:"end_year,omitempty"` // Optional end year
}

type TimelineProduct struct {
	ID         uint                    `json:"id"`
	Name       string                  `json:"name"`
	StartYear  int                     `json:"start_year"`
	EndYear    *int                    `json:"end_year,omitempty"`
	Stages     []TimelineStage         `json:"stages"`
	StatusLogs []TimelineStatusLog     `json:"logs"`
	Components []TimelineComponentInfo `json:"components"`
}

type TimelineGroup struct {
	ID       uint              `json:"id"`
	Name     string            `json:"name"`
	Products []TimelineProduct `json:"products"`
}

type TimelineResponse struct {
	StartYear int             `json:"start_year"`         // Earliest year across all products
	EndYear   *int            `json:"end_year,omitempty"` // Latest year across all products
	Groups    []TimelineGroup `json:"groups"`
}
