package dto

type DashboardSummary struct {
	TotalProducts      int64            `json:"total_products"`
	ActiveProducts     int64            `json:"active_products"`
	InactiveProducts   int64            `json:"inactive_products"`
	DeprecatedProducts int64            `json:"deprecated_products"`
	TotalProductGroups int64            `json:"total_product_groups"`
	TotalComponents    int64            `json:"total_components"`
	TopProducts        []TopProductItem `json:"top_products"`
	TopProductGroups   []TopGroupItem   `json:"top_product_groups"`

	TotalTeams         int64                  `json:"total_teams"`
	TotalDomains       int64                  `json:"total_domains"`
	ResourcePriorities []ResourcePriorityItem `json:"resource_priorities"`
}

type TopProductItem struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	TotalViews int    `json:"total_views"`
}

type TopGroupItem struct {
	ID            uint   `json:"id"`
	Name          string `json:"name"`
	TotalProducts int    `json:"total_products"`
}

type ResourcePriorityItem struct {
	ID                          uint   `json:"id"`
	Name                        string `json:"name"`
	Domain                      string `json:"domain"`
	TotalNormalPriorityResource int    `json:"total_normal_priority_resource"`
	TotalHighPriorityResource   int    `json:"total_high_priority_resource"`
	TotalLowPriorityResource    int    `json:"total_low_priority_resource"`
}
