package dto

import (
	"pvs-api/internal/models"
	"time"
)

// SystemParamRequest định nghĩa DTO cho việc tạo/cập nhật SystemParam
type SystemParamRequest struct {
	ParamType    models.ParamType `json:"param_type" binding:"required" example:"PRODUCT_STATUS"`
	Code         string           `json:"code" binding:"required" example:"ACTIVE"`
	Name         string           `json:"name" binding:"required" example:"Đang hoạt động"`
	DisplayOrder int              `json:"display_order" example:"1"`
	IsActive     bool             `json:"is_active" example:"true"`
}

// SystemParamResponse định nghĩa DTO cho việc trả về thông tin SystemParam
type SystemParamResponse struct {
	ID           uint             `json:"id" example:"1"`
	ParamType    models.ParamType `json:"param_type" example:"PRODUCT_STATUS"`
	Code         string           `json:"code" example:"ACTIVE"`
	Name         string           `json:"name" example:"Đang hoạt động"`
	DisplayOrder int              `json:"display_order" example:"1"`
	IsActive     bool             `json:"is_active" example:"true"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
}

// FromSystemParamModel chuyển đổi từ model sang response DTO
func FromSystemParamModel(model *models.SystemParam) *SystemParamResponse {
	return &SystemParamResponse{
		ID:           model.ID,
		ParamType:    model.ParamType,
		Code:         model.Code,
		Name:         model.Name,
		DisplayOrder: model.DisplayOrder,
		IsActive:     model.IsActive,
		CreatedAt:    model.CreatedAt,
		UpdatedAt:    model.UpdatedAt,
	}
}

// ToSystemParamModel chuyển đổi từ request DTO sang model
func (r *SystemParamRequest) ToSystemParamModel() *models.SystemParam {
	return &models.SystemParam{
		ParamType:    r.ParamType,
		Code:         r.Code,
		Name:         r.Name,
		DisplayOrder: r.DisplayOrder,
		IsActive:     r.IsActive,
	}
}
