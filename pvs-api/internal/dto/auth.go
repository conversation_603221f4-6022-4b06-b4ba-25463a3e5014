package dto

import "github.com/golang-jwt/jwt/v5"

type CustomClaims struct {
	UserID      uint     `json:"user_id"`
	Username    string   `json:"username"`
	RoleCode    string   `json:"role_code"`
	Permissions []string `json:"permissions"` // Thêm permissions vào JWT
	jwt.RegisteredClaims
}

// LoginRequest định nghĩa DTO cho yêu cầu đăng nhập
type LoginRequest struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"Admin@123"`
}

// RefreshTokenRequest định nghĩa DTO cho yêu cầu làm mới token
type RefreshTokenRequest struct {
	Token string `json:"token" binding:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

// AuthResponse định nghĩa DTO cho phản hồi xác thực
type AuthResponse struct {
	Token string   `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	User  UserInfo `json:"user"`
}

// UserInfo định nghĩa thông tin người dùng trong phản hồi xác thực
type UserInfo struct {
	ID          uint     `json:"id" example:"1"`
	Username    string   `json:"username" example:"admin"`
	RoleCode    string   `json:"role_code" example:"ADMIN"`
	Permissions []string `json:"permissions" example:"['user:list', 'user:create']"`
}
