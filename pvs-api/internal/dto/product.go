package dto

import (
	"pvs-api/internal/models"
	"time"
)

// ProductRequest định nghĩa DTO cho việc tạo/cập nhật Product
type ProductRequest struct {
	Name        string `json:"name" binding:"required" example:"Banking App"`
	Description string `json:"description" example:"Ứng dụng ngân hàng di động"`
	Status      string `json:"status" example:"ACTIVE"`
}

// ProductResponse định nghĩa DTO cho phản hồi khi truy vấn Product
type ProductResponse struct {
	ID          uint      `json:"id" example:"1"`
	Name        string    `json:"name" example:"Banking App"`
	Description string    `json:"description" example:"Ứng dụng ngân hàng di động"`
	Status      string    `json:"status" example:"ACTIVE"`
	IsFavorite  bool      `json:"is_favorite" example:"false"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ProductDetailResponse định nghĩa DTO cho phản hồi chi tiết Product (bao gồm các nhóm sản phẩm)
type ProductDetailResponse struct {
	ID            uint                   `json:"id" example:"1"`
	Name          string                 `json:"name" example:"Banking App"`
	Description   string                 `json:"description" example:"Ứng dụng ngân hàng di động"`
	Status        string                 `json:"status" example:"ACTIVE"`
	IsFavorite    bool                   `json:"is_favorite" example:"false"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	ProductGroups []ProductGroupResponse `json:"product_groups,omitempty"`
}

// ProductProductGroupLinkRequest định nghĩa DTO cho việc liên kết Product với ProductGroup
type ProductProductGroupLinkRequest struct {
	ProductID      uint `json:"product_id" binding:"required" example:"1"`
	ProductGroupID uint `json:"product_group_id" binding:"required" example:"1"`
}

// ProductComponentLinkRequest định nghĩa DTO cho việc liên kết Product với Component
type ProductComponentLinkRequest struct {
	ProductID   uint `json:"product_id" binding:"required" example:"1"`
	ComponentID uint `json:"component_id" binding:"required" example:"1"`
}

// SetProductStageRequest định nghĩa DTO cho việc thiết lập giai đoạn mới cho sản phẩm
type SetProductStageRequest struct {
	StageCode string    `json:"stage_code" binding:"required" example:"DEVELOPMENT"`
	StartDate time.Time `json:"start_date" binding:"required"`
	Notes     string    `json:"notes" example:"Chuyển sang giai đoạn phát triển"`
}

// ToProductModel chuyển đổi ProductRequest sang model Product
func (req *ProductRequest) ToProductModel() *models.Product {
	return &models.Product{
		Name:        req.Name,
		Description: req.Description,
		Status:      req.Status,
	}
}

// FromProductModel chuyển đổi model Product sang ProductResponse
func FromProductModel(model *models.Product) ProductResponse {
	return ProductResponse{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		Status:      model.Status,
		IsFavorite:  model.IsFavorite,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	}
}
