package dto

// TechnicalFlowRequest represents the request to create/update a technical flow
type TechnicalFlowRequest struct {
	BusinessStepID uint   `json:"business_step_id" binding:"required"`
	Name           string `json:"name" binding:"required"`
	Description    string `json:"description"`
	DiagramURL     string `json:"diagram_url"`
}

// TechnicalFlowResponse represents the response for a technical flow
type TechnicalFlowResponse struct {
	ID               uint   `json:"id"`
	BusinessStepID   uint   `json:"business_step_id"`
	BusinessStepName string `json:"business_step_name,omitempty"`
	Name             string `json:"name"`
	Description      string `json:"description"`
	DiagramURL       string `json:"diagram_url"`
	StepCount        int    `json:"step_count,omitempty"`
}

// TechnicalStepRequest represents the request to create/update a technical step
type TechnicalStepRequest struct {
	FlowID      uint   `json:"flow_id" binding:"required"`
	StepNumber  int    `json:"step_number" binding:"required"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	ComponentID uint   `json:"component_id" binding:"required"`
}

// TechnicalStepResponse represents the response for a technical step
type TechnicalStepResponse struct {
	ID            uint   `json:"id"`
	FlowID        uint   `json:"flow_id"`
	FlowName      string `json:"flow_name,omitempty"`
	StepNumber    int    `json:"step_number"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	ComponentID   uint   `json:"component_id"`
	ComponentName string `json:"component_name,omitempty"`
}
