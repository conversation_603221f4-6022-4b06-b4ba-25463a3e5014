package dto

import "pvs-api/internal/models"

type NotableSystemRequest struct {
	Name        string `json:"name" binding:"required" example:"Core Banking System"`
	Description string `json:"description" example:"Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn"`
	StartYear   int    `json:"start_year" binding:"required" example:"2020"`
	EndYear     *int   `json:"end_year,omitempty" example:"2023"`
}

type NotableSystemResponse struct {
	ID          uint   `json:"id" example:"1"`
	Name        string `json:"name" example:"Core Banking System"`
	Description string `json:"description" example:"Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn"`
	StartYear   int    `json:"start_year" example:"2020"`
	EndYear     *int   `json:"end_year,omitempty" example:"2023"`
}

func FromNotableSystemModel(system *models.NotableSystem) *NotableSystemResponse {
	return &NotableSystemResponse{
		ID:          system.ID,
		Name:        system.Name,
		Description: system.Description,
		StartYear:   system.StartYear,
		EndYear:     system.EndYear,
	}
}
