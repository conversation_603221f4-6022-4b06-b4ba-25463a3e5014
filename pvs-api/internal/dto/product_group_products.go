package dto

type ProductInGroup struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	CurrentStage string `json:"current_stage"`
	Status       string `json:"status"`
	IsFavorite   bool   `json:"is_favorite"`
	HasUsageData bool   `json:"has_usage_data"`
}

type ProductWithGroup struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	CurrentStage string `json:"current_stage"`
	Status       string `json:"status"`
	IsFavorite   bool   `json:"is_favorite"`
	HasUsageData bool   `json:"has_usage_data"`
	GroupID      uint   `json:"group_id"`
}

type GroupWithProducts struct {
	ID          uint             `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	IsFavorite  bool             `json:"is_favorite"`
	Products    []ProductInGroup `json:"products"`
}

type GroupedProductsResponse struct {
	Groups []GroupWithProducts `json:"groups"`
	Count  int64               `json:"count"` // Số lượng sản phẩm trong trang hiện tại
	Total  int64               `json:"total"` // Tổng số sản phẩm thỏa mãn điều kiện
}
