package dto

import (
	"pvs-api/internal/models"
	"time"
)

// ProductGroupRequest định nghĩa DTO cho việc tạo/cập nhật ProductGroup
type ProductGroupRequest struct {
	Name        string `json:"name" binding:"required" example:"Mobile Apps"`
	Description string `json:"description" example:"Nhóm các ứng dụng di động"`
}

// ProductGroupResponse định nghĩa DTO cho phản hồi khi truy vấn ProductGroup
type ProductGroupResponse struct {
	ID          uint      `json:"id" example:"1"`
	Name        string    `json:"name" example:"Mobile Apps"`
	Description string    `json:"description" example:"Nhóm các ứng dụng di động"`
	IsFavorite  bool      `json:"is_favorite" example:"false"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ProductGroupDetailResponse định nghĩa DTO cho phản hồi chi tiết ProductGroup (bao gồm sản phẩm)
type ProductGroupDetailResponse struct {
	ID          uint              `json:"id" example:"1"`
	Name        string            `json:"name" example:"Mobile Apps"`
	Description string            `json:"description" example:"Nhóm các ứng dụng di động"`
	IsFavorite  bool              `json:"is_favorite" example:"false"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	Products    []ProductResponse `json:"products,omitempty"`
}

// ToProductGroupModel chuyển đổi ProductGroupRequest sang model ProductGroup
func (req *ProductGroupRequest) ToProductGroupModel() *models.ProductGroup {
	return &models.ProductGroup{
		Name:        req.Name,
		Description: req.Description,
	}
}

// FromProductGroupModel chuyển đổi model ProductGroup sang ProductGroupResponse
func FromProductGroupModel(model *models.ProductGroup) ProductGroupResponse {
	return ProductGroupResponse{
		ID:          model.ID,
		Name:        model.Name,
		Description: model.Description,
		IsFavorite:  model.IsFavorite,
		CreatedAt:   model.CreatedAt,
		UpdatedAt:   model.UpdatedAt,
	}
}
