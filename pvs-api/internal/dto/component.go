package dto

import (
	"time"

	"pvs-api/internal/models"
)

// ComponentRequest định nghĩa DTO cho việc tạo/cập nhật Component
type ComponentRequest struct {
	Name              string `json:"name" binding:"required" example:"Authentication Service"`
	Description       string `json:"description" example:"Dịch vụ xác thực người dùng"`
	TargetUserCode    string `json:"target_user_code" binding:"required" example:"CUSTOMER"`
	Importance        string `json:"importance,omitempty"`
	MainFunction      string `json:"main_function,omitempty"`
	ExtraFunction     string `json:"extra_function,omitempty"`
	ComponentTypeCode string `json:"component_type_code" binding:"required" example:"APPLICATION"`
	ParentComponentID *uint  `json:"parent_component_id,omitempty"`
	ConfluenceURL     string `json:"confluence_url,omitempty"`
}

// ComponentResponse định nghĩa DTO cho phản hồi khi truy vấn Component
type ComponentResponse struct {
	ID                uint      `json:"id" example:"1"`
	Name              string    `json:"name" example:"Authentication Service"`
	Description       string    `json:"description" example:"Dịch vụ xác thực người dùng"`
	TargetUserCode    string    `json:"target_user_code" example:"CUSTOMER"`
	Importance        string    `json:"importance,omitempty"`
	MainFunction      string    `json:"main_function,omitempty"`
	ExtraFunction     string    `json:"extra_function,omitempty"`
	ComponentTypeCode string    `json:"component_type_code" example:"APPLICATION"`
	ParentComponentID *uint     `json:"parent_component_id,omitempty" example:"0"`
	ConfluenceURL     string    `json:"confluence_url" example:"https://confluence.example.com/pages/component/123"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ComponentDetailResponse định nghĩa DTO cho phản hồi chi tiết Component
type ComponentDetailResponse struct {
	ID                uint                `json:"id" example:"1"`
	Name              string              `json:"name" example:"Authentication Service"`
	Description       string              `json:"description" example:"Dịch vụ xác thực người dùng"`
	TargetUser        string              `json:"target_user" binding:"required" example:"CUSTOMER"`
	Importance        string              `json:"importance,omitempty"`
	MainFunction      string              `json:"main_function,omitempty"`
	ExtraFunction     string              `json:"extra_function,omitempty"`
	ComponentType     string              `json:"component_type" example:"API_SERVICE"`
	ParentComponentID *uint               `json:"parent_component_id,omitempty" example:"0"`
	ConfluenceURL     string              `json:"confluence_url" example:"https://confluence.example.com/pages/component/123"`
	CreatedAt         time.Time           `json:"created_at"`
	UpdatedAt         time.Time           `json:"updated_at"`
	ParentComponent   *ComponentResponse  `json:"parent_component,omitempty"`
	ChildComponents   []ComponentResponse `json:"child_components,omitempty"`
	Products          []ProductResponse   `json:"products,omitempty"`
}

// ToComponentModel chuyển đổi ComponentRequest sang model Component
func (req *ComponentRequest) ToComponentModel() *models.Component {
	return &models.Component{
		Name:              req.Name,
		Description:       req.Description,
		TargetUserCode:    req.TargetUserCode,
		Importance:        req.Importance,
		MainFunction:      req.MainFunction,
		ExtraFunction:     req.ExtraFunction,
		ComponentTypeCode: req.ComponentTypeCode,
		ParentComponentID: req.ParentComponentID,
		ConfluenceURL:     req.ConfluenceURL,
	}
}

// FromComponentModel chuyển đổi model Component sang ComponentResponse
func FromComponentModel(model *models.Component) ComponentResponse {
	return ComponentResponse{
		ID:                model.ID,
		Name:              model.Name,
		Description:       model.Description,
		TargetUserCode:    model.TargetUserCode,
		Importance:        model.Importance,
		MainFunction:      model.MainFunction,
		ExtraFunction:     model.ExtraFunction,
		ComponentTypeCode: model.ComponentTypeCode,
		ParentComponentID: model.ParentComponentID,
		ConfluenceURL:     model.ConfluenceURL,
		CreatedAt:         model.CreatedAt,
		UpdatedAt:         model.UpdatedAt,
	}
}
