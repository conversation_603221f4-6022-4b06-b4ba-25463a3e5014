package dto

import "time"

// StakeholderRequest định nghĩa DTO cho việc tạo/cập nhật Stakeholder
type StakeholderRequest struct {
	FullName string `json:"full_name" binding:"required" example:"Nguyễn Văn A"`
	Email    string `json:"email" binding:"required" example:"<EMAIL>"`
	Position string `json:"position"`
}

// StakeholderResponse định nghĩa DTO cho phản hồi khi truy vấn Stakeholder
type StakeholderResponse struct {
	ID        uint      `json:"id" example:"1"`
	FullName  string    `json:"full_name" example:"Nguyễn Văn A"`
	Email     string    `json:"email" example:"<EMAIL>"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
