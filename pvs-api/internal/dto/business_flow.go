package dto

// BusinessFlowRequest represents the request to create/update a business flow
type BusinessFlowRequest struct {
    FeatureID   uint   `json:"feature_id" binding:"required"`
    Name        string `json:"name" binding:"required"`
    Description string `json:"description"`
}

// BusinessFlowResponse represents the response for a business flow
type BusinessFlowResponse struct {
    ID          uint   `json:"id"`
    FeatureID   uint   `json:"feature_id"`
    FeatureName string `json:"feature_name,omitempty"`
    Name        string `json:"name"`
    Description string `json:"description"`
    StepCount   int    `json:"step_count,omitempty"`
    TechnicalFlowCount int `json:"technical_flow_count,omitempty"`
}

// BusinessStepRequest represents the request to create/update a business step
type BusinessStepRequest struct {
    FlowID      uint   `json:"flow_id" binding:"required"`
    StepNumber  int    `json:"step_number" binding:"required"`
    Name        string `json:"name" binding:"required"`
    Description string `json:"description"`
    ActorDepartment string `json:"actor_department" binding:"required"`
    ActorRole   string `json:"actor_role" binding:"required"`
}

// BusinessStepResponse represents the response for a business step
type BusinessStepResponse struct {
    ID          uint   `json:"id"`
    FlowID      uint   `json:"flow_id"`
    FlowName    string `json:"flow_name,omitempty"`
    StepNumber  int    `json:"step_number"`
    Name        string `json:"name"`
    Description string `json:"description"`
    ActorDepartment string `json:"actor_department"`
    ActorRole   string `json:"actor_role"`
}