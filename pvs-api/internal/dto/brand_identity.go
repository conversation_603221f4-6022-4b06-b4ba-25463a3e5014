package dto

import (
    "pvs-api/internal/models"
    "time"
)

type BrandIdentityRequest struct {
    CompanyName string     `json:"company_name" binding:"required" example:"Acme Corp"`
    ColorCode   string     `json:"color_code" binding:"required,hexcolor" example:"#FF0000"`
    LogoURL     string     `json:"logo_url" example:"https://example.com/logo.png"`
    EffectiveAt time.Time  `json:"effective_at" binding:"required" example:"2024-01-01T00:00:00Z"`
    ExpiredAt   *time.Time `json:"expired_at,omitempty" example:"2024-12-31T23:59:59Z"`
    Note        string     `json:"note" example:"Rebranding for 2024"`
}

type BrandIdentityResponse struct {
    ID          uint       `json:"id" example:"1"`
    CompanyName string     `json:"company_name" example:"Acme Corp"`
    ColorCode   string     `json:"color_code" example:"#FF0000"`
    LogoURL     string     `json:"logo_url" example:"https://example.com/logo.png"`
    EffectiveAt time.Time  `json:"effective_at" example:"2024-01-01T00:00:00Z"`
    ExpiredAt   *time.Time `json:"expired_at,omitempty" example:"2024-12-31T23:59:59Z"`
    Note        string     `json:"note" example:"Rebranding for 2024"`
    IsActive    bool       `json:"is_active" example:"true"`
    CreatedAt   time.Time  `json:"created_at"`
    UpdatedAt   time.Time  `json:"updated_at"`
}

func FromBrandIdentityModel(model *models.BrandIdentity) *BrandIdentityResponse {
    return &BrandIdentityResponse{
        ID:          model.ID,
        CompanyName: model.CompanyName,
        ColorCode:   model.ColorCode,
        LogoURL:     model.LogoURL,
        EffectiveAt: model.EffectiveAt,
        ExpiredAt:   model.ExpiredAt,
        Note:        model.Note,
        IsActive:    model.IsActive(),
        CreatedAt:   model.CreatedAt,
        UpdatedAt:   model.UpdatedAt,
    }
}