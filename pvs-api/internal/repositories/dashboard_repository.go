package repositories

import (
	"context"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"

	"gorm.io/gorm"
)

type DashboardRepository interface {
	GetSummary(ctx context.Context) (*dto.DashboardSummary, error)
}

type dashboardRepository struct {
	db *gorm.DB
}

func NewDashboardRepository(db *gorm.DB) DashboardRepository {
	return &dashboardRepository{db: db}
}

// L<PERSON>y thống kê tổng quan về sản phẩm và nguồn lực
// Các thông số về sản phẩm
// - Tổng số sản phẩm
// - Số sản phẩm theo trạng thái (active, inactive, deprecated)
// - Tổng số component
// - 5 sản phẩm được xem nhiều nhất
// - 5 nhóm sản phẩm chủ lực
// <PERSON><PERSON><PERSON> thông số về nguồn lực
// - T<PERSON><PERSON> số các nhóm người dùng
// - T<PERSON><PERSON> số các domain nhỏ
// - <PERSON><PERSON> bổ nguồn lực theo domain
func (r *dashboardRepository) GetSummary(ctx context.Context) (*dto.DashboardSummary, error) {
	var summary dto.DashboardSummary

	// Sử dụng các truy vấn riêng biệt với cấu trúc cố định để tránh cached plan error

	// Đếm tổng số sản phẩm
	var totalProducts int64
	if err := r.db.WithContext(ctx).Model(&models.Product{}).Count(&totalProducts).Error; err != nil {
		return nil, err
	}
	summary.TotalProducts = totalProducts

	// Đếm số sản phẩm theo trạng thái
	var activeProducts int64
	if err := r.db.WithContext(ctx).Model(&models.Product{}).Where("status = ?", "active").Count(&activeProducts).Error; err != nil {
		return nil, err
	}
	summary.ActiveProducts = activeProducts

	var inactiveProducts int64
	if err := r.db.WithContext(ctx).Model(&models.Product{}).Where("status = ?", "inactive").Count(&inactiveProducts).Error; err != nil {
		return nil, err
	}
	summary.InactiveProducts = inactiveProducts

	var deprecatedProducts int64
	if err := r.db.WithContext(ctx).Model(&models.Product{}).Where("status = ?", "deprecated").Count(&deprecatedProducts).Error; err != nil {
		return nil, err
	}
	summary.DeprecatedProducts = deprecatedProducts

	// Đếm tổng số component
	var totalComponents int64
	if err := r.db.WithContext(ctx).Model(&models.Component{}).Count(&totalComponents).Error; err != nil {
		return nil, err
	}
	summary.TotalComponents = totalComponents

	// Lấy 5 sản phẩm được xem nhiều nhất
	var topProducts []dto.TopProductItem
	if err := r.db.WithContext(ctx).
		Model(&models.Product{}).
		Select("products.id, products.name, SUM(product_usage_stats.views) as total_views").
		Joins("JOIN product_usage_stats ON products.id = product_usage_stats.product_id").
		Group("products.id, products.name").
		Order("total_views DESC").
		Limit(5).
		Find(&topProducts).Error; err != nil {
		// Không trả về lỗi nếu không có dữ liệu thống kê
		summary.TopProducts = make([]dto.TopProductItem, 0)
	} else {
		summary.TopProducts = topProducts
	}

	// Lấy 5 nhóm sản phẩm chủ lực
	var topGroups []dto.TopGroupItem
	if err := r.db.WithContext(ctx).
		Model(&models.ProductGroup{}).
		Select("product_groups.id, product_groups.name, COUNT(product_product_groups.product_id) as total_products").
		Joins("JOIN product_product_groups ON product_product_groups.product_group_id = product_groups.id").
		Group("product_groups.id, product_groups.name").
		Order("total_products DESC").
		Limit(5).
		Find(&topGroups).Error; err != nil {
		// Không trả về lỗi nếu không có dữ liệu thống kê
		summary.TopProductGroups = make([]dto.TopGroupItem, 0)
	} else {
		summary.TopProductGroups = topGroups
	}

	// Lấy thông tin về nguồn lực
	var totalTeams int64
	if err := r.db.WithContext(ctx).Model(&models.Team{}).Count(&totalTeams).Error; err != nil {
		return nil, err
	}
	summary.TotalTeams = totalTeams

	var totalDomains int64
	if err := r.db.WithContext(ctx).Model(&models.Domain{}).Count(&totalDomains).Error; err != nil {
		return nil, err
	}
	summary.TotalDomains = totalDomains

	return &summary, nil
}
