package repositories

import (
	"context"

	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type ProductStageRepository interface {
	Create(ctx context.Context, productStatus *models.ProductStage) error
	GetByID(ctx context.Context, id uint) (*models.ProductStage, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.ProductStage, int64, error)
	Update(ctx context.Context, productStatus *models.ProductStage) error
	Delete(ctx context.Context, id uint) error
	FindLatestByProductID(ctx context.Context, productID uint) (*models.ProductStage, error)
	ListByProductID(ctx context.Context, productID uint) ([]*models.ProductStage, error)
	AddStakeholder(ctx context.Context, productStatusID, stakeholderID uint, roleCode, tasks string) error
	RemoveStakeholder(ctx context.Context, productStatusID, stakeholderID uint) error
	UpdateStakeholderRole(ctx context.Context, productStatusID, stakeholderID uint, roleCode, tasks string) error
	GetStakeholders(ctx context.Context, productStatusID uint) ([]*models.ProductStageStakeholder, error)
}

type productStatusRepository struct {
	db *gorm.DB
}

func NewProductStageRepository(db *gorm.DB) ProductStageRepository {
	return &productStatusRepository{db: db}
}

func (r *productStatusRepository) Create(ctx context.Context, productStatus *models.ProductStage) error {
	return r.db.WithContext(ctx).Create(productStatus).Error
}

func (r *productStatusRepository) GetByID(ctx context.Context, id uint) (*models.ProductStage, error) {
	var productStatus models.ProductStage

	err := r.db.WithContext(ctx).
		Preload("Product").
		Preload("Stakeholders").
		Preload("Stakeholders.Stakeholder").
		First(&productStatus, id).Error

	if err != nil {
		return nil, err
	}

	return &productStatus, nil
}

func (r *productStatusRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.ProductStage, int64, error) {
	var productStatuses []*models.ProductStage
	var count int64

	query := r.db.WithContext(ctx).Model(&models.ProductStage{})

	// Apply filters if any
	if params.Filters != nil {
		if productID, ok := params.Filters["product_id"].(uint); ok {
			query = query.Where("product_id = ?", productID)
		}

		if stageCode, ok := params.Filters["stage_code"].(string); ok {
			query = query.Where("stage_code = ?", stageCode)
		}

		if active, ok := params.Filters["active"].(bool); ok && active {
			query = query.Where("end_date IS NULL")
		}
	}

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	err := query.
		Scopes(utils.Paginate(params)).
		Preload("Product").
		Order("start_date DESC").
		Find(&productStatuses).Error

	if err != nil {
		return nil, 0, err
	}

	return productStatuses, count, nil
}

func (r *productStatusRepository) Update(ctx context.Context, productStatus *models.ProductStage) error {
	return r.db.WithContext(ctx).Model(productStatus).Updates(productStatus).Error
}

func (r *productStatusRepository) Delete(ctx context.Context, id uint) error {
	// Start a transaction
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Delete stakeholder associations first
	if err := tx.Where("product_stage_id = ?", id).Delete(&models.ProductStageStakeholder{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Then delete the product stage
	if err := tx.Delete(&models.ProductStage{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

func (r *productStatusRepository) ListByProductID(ctx context.Context, productID uint) ([]*models.ProductStage, error) {
	var productStatuses []*models.ProductStage

	err := r.db.WithContext(ctx).
		Where("product_id = ?", productID).
		Order("start_date DESC").
		Find(&productStatuses).Error

	if err != nil {
		return nil, err
	}

	return productStatuses, nil
}

func (r *productStatusRepository) FindLatestByProductID(ctx context.Context, productID uint) (*models.ProductStage, error) {
	var productStatus models.ProductStage

	err := r.db.WithContext(ctx).Model(&models.ProductStage{}).
		Joins("JOIN products ON products.last_stage_id = product_stages.id").
		Where("products.id = ?", productID).
		Limit(1).
		First(&productStatus).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &productStatus, nil
}

func (r *productStatusRepository) AddStakeholder(ctx context.Context, productStatusID, stakeholderID uint, roleCode, tasks string) error {
	productStatusStakeholder := models.ProductStageStakeholder{
		ProductStageID: productStatusID,
		StakeholderID:  stakeholderID,
		RoleCode:       roleCode,
		Tasks:          tasks,
	}

	return r.db.WithContext(ctx).Create(&productStatusStakeholder).Error
}

func (r *productStatusRepository) RemoveStakeholder(ctx context.Context, productStatusID, stakeholderID uint) error {
	return r.db.WithContext(ctx).
		Where("product_stage_id = ? AND stakeholder_id = ?", productStatusID, stakeholderID).
		Delete(&models.ProductStageStakeholder{}).Error
}

func (r *productStatusRepository) UpdateStakeholderRole(ctx context.Context, productStatusID, stakeholderID uint, roleCode, tasks string) error {
	return r.db.WithContext(ctx).
		Model(&models.ProductStageStakeholder{}).
		Where("product_stage_id = ? AND stakeholder_id = ?", productStatusID, stakeholderID).
		Updates(map[string]interface{}{
			"role_code": roleCode,
			"tasks":     tasks,
		}).Error
}

func (r *productStatusRepository) GetStakeholders(ctx context.Context, productStatusID uint) ([]*models.ProductStageStakeholder, error) {
	var stakeholders []*models.ProductStageStakeholder

	err := r.db.WithContext(ctx).
		Where("product_stage_id = ?", productStatusID).
		Preload("Stakeholder").
		Find(&stakeholders).Error

	if err != nil {
		return nil, err
	}

	return stakeholders, nil
}
