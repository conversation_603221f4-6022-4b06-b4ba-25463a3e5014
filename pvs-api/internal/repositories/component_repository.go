package repositories

import (
	"context"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type ComponentRepository interface {
	Create(ctx context.Context, component *models.Component) error
	GetByID(ctx context.Context, id uint) (*models.Component, error)
	List(ctx context.Context, params *utils.Pagination) ([]models.Component, int64, error)
	Update(ctx context.Context, component *models.Component) error
	Delete(ctx context.Context, id uint) error
	ExistByID(ctx context.Context, id uint) (bool, error)
	GetChildComponents(ctx context.Context, parentID uint) ([]*models.Component, error)
	ListForDropdown(ctx context.Context, search string) ([]dto.DropdownItem, error)
}

type componentRepository struct {
	db *gorm.DB
}

func NewComponentRepository(db *gorm.DB) ComponentRepository {
	return &componentRepository{db: db}
}

func (r *componentRepository) ExistByID(ctx context.Context, id uint) (bool, error) {
	var exists bool
	err := r.db.WithContext(ctx).Model(&models.Component{}).Select("count(*) > 0").Where("id = ?", id).Find(&exists).Error
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *componentRepository) Create(ctx context.Context, component *models.Component) error {
	return r.db.WithContext(ctx).Create(component).Error
}

func (r *componentRepository) GetByID(ctx context.Context, id uint) (*models.Component, error) {
	var component models.Component

	err := r.db.WithContext(ctx).
		Preload("ParentComponent").
		Preload("ChildComponents").
		First(&component, id).Error

	if err != nil {
		return nil, err
	}

	return &component, nil
}

func (r *componentRepository) List(ctx context.Context, params *utils.Pagination) ([]models.Component, int64, error) {
	var components []models.Component
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.Component{})

	// Apply filters
	if params.Filters != nil {
		if name, ok := params.Filters["name"].(string); ok && name != "" {
			query = query.Where("name ILIKE ?", "%"+name+"%")
		}

		if parentID, ok := params.Filters["parent_id"].(uint); ok {
			if parentID > 0 {
				query = query.Where("parent_component_id = ?", parentID)
			} else {
				query = query.Where("parent_component_id IS NULL")
			}
		}
	}

	// Tách truy vấn đếm tổng số và lấy dữ liệu để tránh cached plan error
	countQuery := query
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get data with consistent structure
	if err := query.Scopes(utils.Paginate(params)).
		Order("name ASC").
		Find(&components).Error; err != nil {
		return nil, 0, err
	}

	return components, total, nil
}

func (r *componentRepository) Update(ctx context.Context, component *models.Component) error {
	return r.db.WithContext(ctx).Model(component).Updates(component).Error
}

func (r *componentRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Component{}, id).Error
}

func (r *componentRepository) GetChildComponents(ctx context.Context, parentID uint) ([]*models.Component, error) {
	var components []*models.Component

	err := r.db.WithContext(ctx).
		Where("parent_component_id = ?", parentID).
		Find(&components).Error

	if err != nil {
		return nil, err
	}

	return components, nil
}

func (r *componentRepository) ListForDropdown(ctx context.Context, search string) ([]dto.DropdownItem, error) {
	var items []dto.DropdownItem
	query := r.db.WithContext(ctx).Model(&models.Component{}).
		Select("id, name").
		Order("name ASC")

	if search != "" {
		query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+search+"%")
	}

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}
