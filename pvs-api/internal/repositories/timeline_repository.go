package repositories

import (
	"context"
	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"time"

	"gorm.io/gorm"
)

type TimelineRepository interface {
	GetTimeline(ctx context.Context) (*dto.TimelineResponse, error)
}

type timelineRepository struct {
	db *gorm.DB
}

func NewTimelineRepository(db *gorm.DB) TimelineRepository {
	return &timelineRepository{db: db}
}

func (r *timelineRepository) GetTimeline(ctx context.Context) (*dto.TimelineResponse, error) {
	var groups []models.ProductGroup
	response := dto.TimelineResponse{
		StartYear: time.Now().Year(),            // Initialize with current year
		Groups:    make([]dto.TimelineGroup, 0), // Initialize empty slice to avoid nil
	}

	// Get all product groups with their products
	if err := r.db.WithContext(ctx).
		Preload("Products").
		Find(&groups).Error; err != nil {
		return nil, err
	}

	// Process each group
	for _, group := range groups {
		timelineGroup := dto.TimelineGroup{
			ID:       group.ID,
			Name:     group.Name,
			Products: make([]dto.TimelineProduct, 0), // Initialize empty slice to avoid nil
		}

		// Process products in the group
		for _, product := range group.Products {
			timelineProduct := dto.TimelineProduct{
				ID:         product.ID,
				Name:       product.Name,
				Stages:     make([]dto.TimelineStage, 0),
				StatusLogs: make([]dto.TimelineStatusLog, 0),
				Components: make([]dto.TimelineComponentInfo, 0),
			}

			// Get stages for the product - use consistent query structure
			var stages []models.ProductStage
			stageQuery := r.db.WithContext(ctx).
				Model(&models.ProductStage{}).
				Where("product_id = ?", product.ID).
				Order("start_date")

			if err := stageQuery.Find(&stages).Error; err != nil {
				return nil, err
			}

			// Find product's overall start and end years from stages
			if len(stages) > 0 {
				firstStage := stages[0]
				timelineProduct.StartYear = firstStage.StartDate.Year()

				// Update global timeline start year if needed
				if timelineProduct.StartYear < response.StartYear {
					response.StartYear = timelineProduct.StartYear
				}

				// Process stages
				for _, stage := range stages {
					timelineStage := dto.TimelineStage{
						Name:      stage.StageCode,
						StartYear: stage.StartDate.Year(),
					}

					if stage.EndDate != nil {
						endYear := stage.EndDate.Year()
						timelineStage.EndYear = &endYear

						// Update global timeline end year if needed
						if endYear > 0 && (response.EndYear == nil || *response.EndYear < endYear) {
							response.EndYear = &endYear
						}
					}

					timelineProduct.Stages = append(timelineProduct.Stages, timelineStage)
				}
			}

			// Get status logs for the product - use consistent query structure
			var statusLogs []models.ProductStatusLog
			statusQuery := r.db.WithContext(ctx).
				Model(&models.ProductStatusLog{}).
				Where("product_id = ?", product.ID).
				Order("changed_at")

			if err := statusQuery.Find(&statusLogs).Error; err != nil {
				return nil, err
			}

			// Process status logs
			for i, log := range statusLogs {
				startYear := log.ChangedAt.Year()
				endYear := 0

				// If there's a next status log, use its year as the end year
				if i < len(statusLogs)-1 {
					endYear = statusLogs[i+1].ChangedAt.Year()
				}

				statusLog := dto.TimelineStatusLog{
					Status:    log.Status,
					StartYear: startYear,
				}
				if endYear > 0 {
					statusLog.EndYear = &endYear
				}

				timelineProduct.StatusLogs = append(timelineProduct.StatusLogs, statusLog)

				// Update global timeline years if needed
				if startYear < response.StartYear {
					response.StartYear = startYear
				}
				if endYear > 0 && (response.EndYear == nil || *response.EndYear < endYear) {
					response.EndYear = &endYear
				}
			}

			// Get components for the product - use a more consistent approach
			var components []models.Component
			// Sử dụng query trực tiếp thay vì Association để đảm bảo tính nhất quán
			componentQuery := r.db.WithContext(ctx).
				Model(&models.Component{}).
				Joins("JOIN product_components ON product_components.component_id = components.id").
				Where("product_components.product_id = ?", product.ID)

			if err := componentQuery.Find(&components).Error; err != nil {
				return nil, err
			}

			for _, component := range components {
				timelineProduct.Components = append(timelineProduct.Components, dto.TimelineComponentInfo{
					Name: component.Name,
					Link: component.ConfluenceURL,
				})
			}

			timelineGroup.Products = append(timelineGroup.Products, timelineProduct)
		}

		// Chỉ thêm nhóm nếu có sản phẩm
		if len(timelineGroup.Products) > 0 {
			response.Groups = append(response.Groups, timelineGroup)
		}
	}

	// Đảm bảo luôn có EndYear nếu không có dữ liệu
	if response.EndYear == nil {
		currentYear := time.Now().Year()
		response.EndYear = &currentYear
	}

	return &response, nil
}
