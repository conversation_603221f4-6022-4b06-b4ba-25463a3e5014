package repositories

import (
	"context"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

// SystemParamRepository định nghĩa các phương thức để thao tác với bảng system_params
type SystemParamRepository interface {
	Create(ctx context.Context, param *models.SystemParam) error
	GetByID(ctx context.Context, id uint) (*models.SystemParam, error)
	GetByCode(ctx context.Context, paramType models.ParamType, code string) (*models.SystemParam, error)
	GetByType(ctx context.Context, paramType models.ParamType) ([]*models.SystemParam, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.SystemParam, int64, error)
	Update(ctx context.Context, param *models.SystemParam) error
	Delete(ctx context.Context, id uint) error
	GetActiveByType(ctx context.Context, paramType string) ([]*models.SystemParam, error)
}

type systemParamRepository struct {
	db *gorm.DB
}

// NewSystemParamRepository tạo một instance mới của SystemParamRepository
func NewSystemParamRepository(db *gorm.DB) SystemParamRepository {
	return &systemParamRepository{db: db}
}

// Create tạo một tham số hệ thống mới
func (r *systemParamRepository) Create(ctx context.Context, param *models.SystemParam) error {
	return r.db.WithContext(ctx).Create(param).Error
}

// GetByID lấy tham số hệ thống theo ID
func (r *systemParamRepository) GetByID(ctx context.Context, id uint) (*models.SystemParam, error) {
	var param models.SystemParam
	err := r.db.WithContext(ctx).First(&param, id).Error
	if err != nil {
		return nil, err
	}
	return &param, nil
}

// GetByCode lấy tham số hệ thống theo loại và mã
func (r *systemParamRepository) GetByCode(ctx context.Context, paramType models.ParamType, code string) (*models.SystemParam, error) {
	var param models.SystemParam
	err := r.db.WithContext(ctx).
		Where("param_type = ? AND code = ?", paramType, code).
		First(&param).Error
	if err != nil {
		return nil, err
	}
	return &param, nil
}

// GetByType lấy tất cả tham số hệ thống theo loại
func (r *systemParamRepository) GetByType(ctx context.Context, paramType models.ParamType) ([]*models.SystemParam, error) {
	var params []*models.SystemParam
	err := r.db.WithContext(ctx).
		Where("param_type = ?", paramType).
		Order("order ASC").
		Find(&params).Error
	if err != nil {
		return nil, err
	}
	return params, nil
}

// GetActiveByType lấy tất cả tham số hệ thống đang kích hoạt theo loại
func (r *systemParamRepository) GetActiveByType(ctx context.Context, paramType string) ([]*models.SystemParam, error) {
	var params []*models.SystemParam
	err := r.db.WithContext(ctx).
		Where("param_type = ? AND is_active = ?", paramType, true).
		Order("order ASC").
		Find(&params).Error
	if err != nil {
		return nil, err
	}
	return params, nil
}

// List lấy danh sách tham số hệ thống có phân trang
func (r *systemParamRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.SystemParam, int64, error) {
	var systemParams []*models.SystemParam
	var count int64

	query := r.db.WithContext(ctx).Model(&models.SystemParam{})

	// Apply filters if any
	if params.Filters != nil {
		if isActive, ok := params.Filters["is_active"].(bool); ok {
			query = query.Where("is_active = ?", isActive)
		}
		if paramType, ok := params.Filters["param_type"].(string); ok && paramType != "" {
			query = query.Where("param_type = ?", paramType)
		}
	}

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and ordering
	err := query.
		Scopes(utils.Paginate(params)).
		Order("param_type ASC, display_order ASC").
		Find(&systemParams).Error

	if err != nil {
		return nil, 0, err
	}

	return systemParams, count, nil
}

// Update cập nhật thông tin tham số hệ thống
func (r *systemParamRepository) Update(ctx context.Context, param *models.SystemParam) error {
	return r.db.WithContext(ctx).Save(param).Error
}

// Delete xóa tham số hệ thống
func (r *systemParamRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.SystemParam{}, id).Error
}
