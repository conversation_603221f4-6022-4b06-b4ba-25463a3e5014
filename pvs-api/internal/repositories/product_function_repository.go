package repositories

import (
	"context"
	"pvs-api/internal/dto"
	"pvs-api/pkg/utils"
)

type ProductFunctionRepository interface {
	// Product Function methods
	ListProductFunctions(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.ProductFunctionResponse, int64, error)
	GetProductFunctionByID(ctx context.Context, id uint) (*dto.ProductFunctionResponse, error)
	CreateProductFunction(ctx context.Context, request *dto.ProductFunctionRequest) (*dto.ProductFunctionResponse, error)
	UpdateProductFunction(ctx context.Context, id uint, request *dto.ProductFunctionRequest) (*dto.ProductFunctionResponse, error)
	DeleteProductFunction(ctx context.Context, id uint) error

	// Feature methods
	ListFeatures(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.FeatureResponse, int64, error)
	GetFeatureByID(ctx context.Context, id uint) (*dto.FeatureResponse, error)
	CreateFeature(ctx context.Context, request *dto.FeatureRequest) (*dto.FeatureResponse, error)
	UpdateFeature(ctx context.Context, id uint, request *dto.FeatureRequest) (*dto.FeatureResponse, error)
	DeleteFeature(ctx context.Context, id uint) error
}

type BusinessFlowRepository interface {
	// Business Flow methods
	ListBusinessFlows(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.BusinessFlowResponse, int64, error)
	GetBusinessFlowByID(ctx context.Context, id uint) (*dto.BusinessFlowResponse, error)
	CreateBusinessFlow(ctx context.Context, request *dto.BusinessFlowRequest) (*dto.BusinessFlowResponse, error)
	UpdateBusinessFlow(ctx context.Context, id uint, request *dto.BusinessFlowRequest) (*dto.BusinessFlowResponse, error)
	DeleteBusinessFlow(ctx context.Context, id uint) error

	// Business Step methods
	ListBusinessSteps(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.BusinessStepResponse, int64, error)
	GetBusinessStepByID(ctx context.Context, id uint) (*dto.BusinessStepResponse, error)
	CreateBusinessStep(ctx context.Context, request *dto.BusinessStepRequest) (*dto.BusinessStepResponse, error)
	UpdateBusinessStep(ctx context.Context, id uint, request *dto.BusinessStepRequest) (*dto.BusinessStepResponse, error)
	DeleteBusinessStep(ctx context.Context, id uint) error
}

type TechnicalFlowRepository interface {
	// Technical Flow methods
	ListTechnicalFlows(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.TechnicalFlowResponse, int64, error)
	GetTechnicalFlowByID(ctx context.Context, id uint) (*dto.TechnicalFlowResponse, error)
	CreateTechnicalFlow(ctx context.Context, request *dto.TechnicalFlowRequest) (*dto.TechnicalFlowResponse, error)
	UpdateTechnicalFlow(ctx context.Context, id uint, request *dto.TechnicalFlowRequest) (*dto.TechnicalFlowResponse, error)
	DeleteTechnicalFlow(ctx context.Context, id uint) error

	// Technical Step methods
	ListTechnicalSteps(ctx context.Context, filters map[string]interface{}, pagination *utils.Pagination) ([]dto.TechnicalStepResponse, int64, error)
	GetTechnicalStepByID(ctx context.Context, id uint) (*dto.TechnicalStepResponse, error)
	CreateTechnicalStep(ctx context.Context, request *dto.TechnicalStepRequest) (*dto.TechnicalStepResponse, error)
	UpdateTechnicalStep(ctx context.Context, id uint, request *dto.TechnicalStepRequest) (*dto.TechnicalStepResponse, error)
	DeleteTechnicalStep(ctx context.Context, id uint) error
}
