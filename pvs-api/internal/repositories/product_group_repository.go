package repositories

import (
	"context"

	"pvs-api/internal/dto"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type ProductGroupRepository interface {
	Create(ctx context.Context, productGroup *models.ProductGroup) error
	GetByID(ctx context.Context, id uint) (*models.ProductGroup, error)
	List(ctx context.Context, params *utils.Pagination) ([]models.ProductGroup, int64, error)
	Update(ctx context.Context, productGroup *models.ProductGroup) error
	UpdateFavoriteStatus(ctx context.Context, id uint, isFavorite bool) error
	Delete(ctx context.Context, id uint) error
	ExistByID(ctx context.Context, id uint) (bool, error)
	ListProducts(ctx context.Context, productGroupID uint) ([]*models.Product, error)
	ListForDropdown(ctx context.Context, search string) ([]dto.DropdownItem, error)
}

type productGroupRepository struct {
	db *gorm.DB
}

func NewProductGroupRepository(db *gorm.DB) ProductGroupRepository {
	return &productGroupRepository{db: db}
}

func (r *productGroupRepository) Create(ctx context.Context, productGroup *models.ProductGroup) error {
	return r.db.WithContext(ctx).Create(productGroup).Error
}

func (r *productGroupRepository) GetByID(ctx context.Context, id uint) (*models.ProductGroup, error) {
	var group models.ProductGroup

	// Sử dụng truy vấn cố định để tránh cached plan error
	err := r.db.WithContext(ctx).
		Model(&models.ProductGroup{}).
		First(&group, id).Error

	if err != nil {
		return nil, err
	}

	return &group, nil
}

func (r *productGroupRepository) ExistByID(ctx context.Context, id uint) (bool, error) {
	var exists bool
	err := r.db.WithContext(ctx).Model(&models.ProductGroup{}).Select("count(*) > 0").Where("id = ?", id).Find(&exists).Error
	if err != nil {
		return false, err
	}

	return exists, nil
}

func (r *productGroupRepository) UpdateFavoriteStatus(ctx context.Context, id uint, isFavorite bool) error {
	return r.db.WithContext(ctx).Model(&models.ProductGroup{}).Where("id = ?", id).Update("is_favorite", isFavorite).Error
}

func (r *productGroupRepository) List(ctx context.Context, params *utils.Pagination) ([]models.ProductGroup, int64, error) {
	var groups []models.ProductGroup
	var total int64

	// Tách truy vấn đếm tổng số và lấy dữ liệu để tránh cached plan error
	countQuery := r.db.WithContext(ctx).Model(&models.ProductGroup{})
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Truy vấn lấy dữ liệu với cấu trúc cố định
	query := r.db.WithContext(ctx).Model(&models.ProductGroup{})
	if err := query.Scopes(utils.Paginate(params)).
		Order("name ASC").
		Find(&groups).Error; err != nil {
		return nil, 0, err
	}

	return groups, total, nil
}

func (r *productGroupRepository) Update(ctx context.Context, productGroup *models.ProductGroup) error {
	return r.db.WithContext(ctx).Model(productGroup).Updates(productGroup).Error
}

func (r *productGroupRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ProductGroup{}, id).Error
}

func (r *productGroupRepository) ListProducts(ctx context.Context, productGroupID uint) ([]*models.Product, error) {
	var products []*models.Product

	err := r.db.WithContext(ctx).
		Joins("JOIN product_product_groups ON product_product_groups.product_id = products.id").
		Where("product_product_groups.product_group_id = ?", productGroupID).
		Find(&products).Error

	if err != nil {
		return nil, err
	}

	return products, nil
}

func (r *productGroupRepository) ListForDropdown(ctx context.Context, search string) ([]dto.DropdownItem, error) {
	var items []dto.DropdownItem
	query := r.db.WithContext(ctx).Model(&models.ProductGroup{}).
		Select("id, name").
		Order("name ASC")

	if search != "" {
		query = query.Where("LOWER(name) LIKE LOWER(?)", "%"+search+"%")
	}

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}
