package repositories

import (
	"context"

	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type NotableSystemRepository interface {
	Create(ctx context.Context, system *models.NotableSystem) error
	Update(ctx context.Context, system *models.NotableSystem) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*models.NotableSystem, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.NotableSystem, int64, error)
	ListByYear(ctx context.Context, year int) ([]*models.NotableSystem, error)
}

type notableSystemRepository struct {
	db *gorm.DB
}

func NewNotableSystemRepository(db *gorm.DB) NotableSystemRepository {
	return &notableSystemRepository{db: db}
}

func (r *notableSystemRepository) Create(ctx context.Context, system *models.NotableSystem) error {
	return r.db.WithContext(ctx).Create(system).Error
}

func (r *notableSystemRepository) Update(ctx context.Context, system *models.NotableSystem) error {
	return r.db.WithContext(ctx).Save(system).Error
}

func (r *notableSystemRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.NotableSystem{}, id).Error
}

func (r *notableSystemRepository) GetByID(ctx context.Context, id uint) (*models.NotableSystem, error) {
	var system models.NotableSystem
	err := r.db.WithContext(ctx).First(&system, id).Error
	if err != nil {
		return nil, err
	}
	return &system, nil
}

func (r *notableSystemRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.NotableSystem, int64, error) {
	var systems []*models.NotableSystem
	var count int64

	query := r.db.WithContext(ctx).Model(&models.NotableSystem{})

	// Apply filters if any
	if params.Filters != nil {
		if year, ok := params.Filters["year"].(int); ok {
			query = query.Where("start_year <= ? AND (end_year >= ? OR end_year IS NULL)", year, year)
		}
	}

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated records
	err := query.Scopes(utils.Paginate(params)).Find(&systems).Error
	if err != nil {
		return nil, 0, err
	}

	return systems, count, nil
}

func (r *notableSystemRepository) ListByYear(ctx context.Context, year int) ([]*models.NotableSystem, error) {
	var systems []*models.NotableSystem
	err := r.db.WithContext(ctx).
		Where("start_year <= ? AND (end_year >= ? OR end_year IS NULL)", year, year).
		Find(&systems).Error
	if err != nil {
		return nil, err
	}
	return systems, nil
}
