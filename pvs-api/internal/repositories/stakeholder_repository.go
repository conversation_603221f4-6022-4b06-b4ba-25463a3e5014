package repositories

import (
	"context"

	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type StakeholderRepository interface {
	Create(ctx context.Context, stakeholder *models.Stakeholder) error
	GetByID(ctx context.Context, id uint) (*models.Stakeholder, error)
	List(ctx context.Context, params *utils.Pagination) ([]models.Stakeholder, int64, error)
	Update(ctx context.Context, stakeholder *models.Stakeholder) error
	Delete(ctx context.Context, id uint) error
	GetByEmail(ctx context.Context, email string) (*models.Stakeholder, error)
}

type stakeholderRepository struct {
	db *gorm.DB
}

func NewStakeholderRepository(db *gorm.DB) StakeholderRepository {
	return &stakeholderRepository{db: db}
}

func (r *stakeholderRepository) Create(ctx context.Context, stakeholder *models.Stakeholder) error {
	return r.db.WithContext(ctx).Create(stakeholder).Error
}

func (r *stakeholderRepository) GetByID(ctx context.Context, id uint) (*models.Stakeholder, error) {
	var stakeholder models.Stakeholder

	err := r.db.WithContext(ctx).First(&stakeholder, id).Error
	if err != nil {
		return nil, err
	}

	return &stakeholder, nil
}

func (r *stakeholderRepository) List(ctx context.Context, params *utils.Pagination) ([]models.Stakeholder, int64, error) {
	var stakeholders []models.Stakeholder
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Stakeholder{})

	// Apply filters
	if params.Filters != nil {
		if name, ok := params.Filters["name"].(string); ok && name != "" {
			query = query.Where("name ILIKE ?", "%"+name+"%")
		}

		if department, ok := params.Filters["department"].(string); ok && department != "" {
			query = query.Where("department ILIKE ?", "%"+department+"%")
		}
	}

	// Tách truy vấn đếm tổng số và lấy dữ liệu để tránh cached plan error
	countQuery := query
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get data with consistent structure
	if err := query.Scopes(utils.Paginate(params)).
		Order("name ASC").
		Find(&stakeholders).Error; err != nil {
		return nil, 0, err
	}

	return stakeholders, total, nil
}

func (r *stakeholderRepository) Update(ctx context.Context, stakeholder *models.Stakeholder) error {
	return r.db.WithContext(ctx).Model(stakeholder).Updates(stakeholder).Error
}

func (r *stakeholderRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.Stakeholder{}, id).Error
}

func (r *stakeholderRepository) GetByEmail(ctx context.Context, email string) (*models.Stakeholder, error) {
	var stakeholder models.Stakeholder

	err := r.db.WithContext(ctx).Where("email = ?", email).First(&stakeholder).Error
	if err != nil {
		return nil, err
	}

	return &stakeholder, nil
}
