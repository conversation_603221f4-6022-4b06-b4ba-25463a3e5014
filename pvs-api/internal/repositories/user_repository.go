package repositories

import (
	"context"

	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type UserRepository interface {
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id uint) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	List(ctx context.Context, params *utils.Pagination) ([]models.User, int64, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id uint) error
	UpdateLastLogin(ctx context.Context, id uint) error
	ChangePassword(ctx context.Context, id uint, newPassword string) error
	UpdateRole(ctx context.Context, id uint, roleCode string) error
}

type userRepository struct {
	db *gorm.DB
}

func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

func (r *userRepository) Create(ctx context.Context, user *models.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

func (r *userRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("username = ?", username).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("email = ?", email).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *userRepository) List(ctx context.Context, params *utils.Pagination) ([]models.User, int64, error) {
	var users []models.User
	var total int64

	// Base query
	query := r.db.WithContext(ctx).Model(&models.User{})

	// Apply filters
	if params.Filters != nil {
		if username, ok := params.Filters["username"].(string); ok && username != "" {
			query = query.Where("username ILIKE ?", "%"+username+"%")
		}

		if email, ok := params.Filters["email"].(string); ok && email != "" {
			query = query.Where("email ILIKE ?", "%"+email+"%")
		}

		if roleID, ok := params.Filters["role_id"].(uint); ok && roleID > 0 {
			query = query.Where("role_id = ?", roleID)
		}
	}

	// Tách truy vấn đếm tổng số và lấy dữ liệu để tránh cached plan error
	countQuery := query
	if err := countQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and get data with consistent structure
	if err := query.Scopes(utils.Paginate(params)).
		Preload("Role").
		Order("username ASC").
		Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

func (r *userRepository) Update(ctx context.Context, user *models.User) error {
	return r.db.WithContext(ctx).Model(user).Updates(user).Error
}

func (r *userRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.User{}, id).Error
}

func (r *userRepository) UpdateLastLogin(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", id).
		Update("last_login", gorm.Expr("NOW()")).Error
}

func (r *userRepository) ChangePassword(ctx context.Context, id uint, newPassword string) error {
	user := &models.User{
		ID:       id,
		Password: newPassword,
	}
	// The BeforeSave hook will hash the password
	return r.db.WithContext(ctx).Model(user).
		Select("password").
		Updates(user).Error
}

func (r *userRepository) UpdateRole(ctx context.Context, id uint, roleCode string) error {
	return r.db.WithContext(ctx).Model(&models.User{}).
		Where("id = ?", id).
		Update("role_code", roleCode).Error
}
