package repositories

import (
	"context"
	"pvs-api/internal/models"
	"time"

	"gorm.io/gorm"
)

type ProductUsageRepository interface {
	UpsertStat(ctx context.Context, stat *models.ProductUsageStat) error
	GetStatsByProduct(ctx context.Context, productID uint, startMonth, endMonth time.Time) ([]models.ProductUsageStat, error)
	GetStatsByMonth(ctx context.Context, month time.Time) ([]models.ProductUsageStat, error)
	GetStatsMultiProducts(ctx context.Context, productIDs []uint, startMonth, endMonth time.Time) ([]models.ProductUsageStat, error)
	GetStatsAllProducts(ctx context.Context, startMonth, endMonth time.Time) ([]models.ProductUsageStat, error)
}

type productUsageRepository struct {
	db *gorm.DB
}

func NewProductUsageRepository(db *gorm.DB) ProductUsageRepository {
	return &productUsageRepository{db: db}
}

func (r *productUsageRepository) UpsertStat(ctx context.Context, stat *models.ProductUsageStat) error {
	// Truncate time to first day of month
	stat.Month = time.Date(stat.Month.Year(), stat.Month.Month(), 1, 0, 0, 0, 0, time.UTC)

	// Upsert operation
	result := r.db.WithContext(ctx).
		Where("product_id = ? AND month = ?", stat.ProductID, stat.Month).
		Assign(models.ProductUsageStat{Users: stat.Users}).
		FirstOrCreate(stat)

	return result.Error
}

func (r *productUsageRepository) GetStatsByProduct(ctx context.Context, productID uint, startMonth, endMonth time.Time) ([]models.ProductUsageStat, error) {
	var stats []models.ProductUsageStat

	err := r.db.WithContext(ctx).
		Where("product_id = ? AND month BETWEEN ? AND ?", productID, startMonth, endMonth).
		Order("month ASC").
		Find(&stats).Error

	return stats, err
}

func (r *productUsageRepository) GetStatsByMonth(ctx context.Context, month time.Time) ([]models.ProductUsageStat, error) {
	var stats []models.ProductUsageStat

	// Truncate to first day of month
	firstDayOfMonth := time.Date(month.Year(), month.Month(), 1, 0, 0, 0, 0, time.UTC)

	err := r.db.WithContext(ctx).
		Where("month = ?", firstDayOfMonth).
		Preload("Product").
		Find(&stats).Error

	return stats, err
}

func (r *productUsageRepository) GetStatsMultiProducts(ctx context.Context, productIDs []uint, startDate, endDate time.Time) ([]models.ProductUsageStat, error) {
	var stats []models.ProductUsageStat

	err := r.db.WithContext(ctx).
		Where("product_id IN ? AND month BETWEEN ? AND ?", productIDs, startDate, endDate).
		Preload("Product").
		Order("product_id ASC, month ASC").
		Find(&stats).Error

	return stats, err
}

func (r *productUsageRepository) GetStatsAllProducts(ctx context.Context, startMonth, endMonth time.Time) ([]models.ProductUsageStat, error) {
	var stats []models.ProductUsageStat

	// Đảm bảo startMonth là ngày đầu tháng và endMonth là ngày cuối tháng
	startMonth = time.Date(startMonth.Year(), startMonth.Month(), 1, 0, 0, 0, 0, time.UTC)
	endMonth = time.Date(endMonth.Year(), endMonth.Month(), 1, 0, 0, 0, 0, time.UTC)

	err := r.db.WithContext(ctx).
		Where("month BETWEEN ? AND ?", startMonth, endMonth).
		Preload("Product").
		Order("product_id ASC, month ASC").
		Find(&stats).Error

	return stats, err
}
