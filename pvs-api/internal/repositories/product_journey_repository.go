package repositories

import (
	"context"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type ProductJourneyRepository interface {
	List(ctx context.Context, params *utils.Pagination, filters *ProductJourneyFilters) ([]*models.ProductJourney, int64, error)
	GetByID(ctx context.Context, id uint) (*models.ProductJourney, error)
	Create(ctx context.Context, journey *models.ProductJourney) error
	Update(ctx context.Context, journey *models.ProductJourney) error
	Delete(ctx context.Context, id uint) error
	GetByProductID(ctx context.Context, productID uint) ([]*models.ProductJourney, error)
}

type ProductJourneyFilters struct {
	ProductID uint   `form:"product_id"`
	Status    string `form:"status"`
	Search    string `form:"search"`
}

type productJourneyRepository struct {
	db *gorm.DB
}

func NewProductJourneyRepository(db *gorm.DB) ProductJourneyRepository {
	return &productJourneyRepository{db: db}
}

func (r *productJourneyRepository) List(ctx context.Context, params *utils.Pagination, filters *ProductJourneyFilters) ([]*models.ProductJourney, int64, error) {
	var journeys []*models.ProductJourney
	var total int64

	query := r.db.WithContext(ctx).Model(&models.ProductJourney{})

	// Apply filters
	if filters != nil {
		if filters.ProductID > 0 {
			query = query.Where("product_id = ?", filters.ProductID)
		}
		if filters.Status != "" {
			query = query.Where("status = ?", filters.Status)
		}
		if filters.Search != "" {
			query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+filters.Search+"%", "%"+filters.Search+"%")
		}
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and preload relationships
	query = query.Preload("Product").
		Preload("Components")

	if params != nil {
		query = query.Scopes(utils.Paginate(params))
	}

	if err := query.Find(&journeys).Error; err != nil {
		return nil, 0, err
	}

	return journeys, total, nil
}

func (r *productJourneyRepository) GetByID(ctx context.Context, id uint) (*models.ProductJourney, error) {
	var journey models.ProductJourney
	err := r.db.WithContext(ctx).
		Preload("Product").
		Preload("Components").
		First(&journey, id).Error
	if err != nil {
		return nil, err
	}
	return &journey, nil
}

func (r *productJourneyRepository) Create(ctx context.Context, journey *models.ProductJourney) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Store components temporarily
		components := journey.Components
		journey.Components = nil

		// Create the journey first
		if err := tx.Create(journey).Error; err != nil {
			return err
		}

		// Associate components if provided
		if components != nil && len(components) > 0 {
			if err := tx.Model(journey).Association("Components").Append(components); err != nil {
				return err
			}
		}

		return nil
	})
}

func (r *productJourneyRepository) Update(ctx context.Context, journey *models.ProductJourney) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Store components temporarily
		components := journey.Components
		journey.Components = nil

		// Update the journey
		if err := tx.Model(journey).Updates(journey).Error; err != nil {
			return err
		}

		// Replace all components
		if err := tx.Model(journey).Association("Components").Replace(components); err != nil {
			return err
		}

		return nil
	})
}

func (r *productJourneyRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get the journey first
		var journey models.ProductJourney
		if err := tx.First(&journey, id).Error; err != nil {
			return err
		}

		// Clear all component associations
		if err := tx.Model(&journey).Association("Components").Clear(); err != nil {
			return err
		}

		// Delete the journey
		return tx.Delete(&journey).Error
	})
}

func (r *productJourneyRepository) GetByProductID(ctx context.Context, productID uint) ([]*models.ProductJourney, error) {
	var journeys []*models.ProductJourney
	err := r.db.WithContext(ctx).
		Where("product_id = ?", productID).
		Preload("Product").
		Preload("Components").
		Find(&journeys).Error
	if err != nil {
		return nil, err
	}
	return journeys, nil
}
