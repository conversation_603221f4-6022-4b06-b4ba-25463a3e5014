package repositories

import (
	"context"
	"pvs-api/internal/models"
	"pvs-api/pkg/utils"
	"time"

	"gorm.io/gorm"
)

type BrandIdentityRepository interface {
	Create(ctx context.Context, identity *models.BrandIdentity) error
	GetByID(ctx context.Context, id uint) (*models.BrandIdentity, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.BrandIdentity, int64, error)
	Update(ctx context.Context, identity *models.BrandIdentity) error
	Delete(ctx context.Context, id uint) error
	GetActive(ctx context.Context) (*models.BrandIdentity, error)
}

type brandIdentityRepository struct {
	db *gorm.DB
}

func NewBrandIdentityRepository(db *gorm.DB) BrandIdentityRepository {
	return &brandIdentityRepository{db: db}
}

func (r *brandIdentityRepository) Create(ctx context.Context, identity *models.BrandIdentity) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// If there's an active identity, set its expiredAt to the new identity's effectiveAt
		var active models.BrandIdentity
		if err := tx.Where("expired_at IS NULL").First(&active).Error; err == nil {
			active.ExpiredAt = &identity.EffectiveAt
			if err := tx.Save(&active).Error; err != nil {
				return err
			}
		}

		// Create the new identity
		return tx.Create(identity).Error
	})
}

func (r *brandIdentityRepository) GetActive(ctx context.Context) (*models.BrandIdentity, error) {
	var identity models.BrandIdentity
	now := time.Now()
	err := r.db.WithContext(ctx).
		Where("effective_at <= ?", now).
		Where("expired_at IS NULL OR expired_at > ?", now).
		First(&identity).Error
	if err != nil {
		return nil, err
	}
	return &identity, nil
}

func (r *brandIdentityRepository) GetByID(ctx context.Context, id uint) (*models.BrandIdentity, error) {
	var identity models.BrandIdentity
	err := r.db.WithContext(ctx).First(&identity, id).Error
	if err != nil {
		return nil, err
	}
	return &identity, nil
}

func (r *brandIdentityRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.BrandIdentity, int64, error) {
	var identities []*models.BrandIdentity
	var count int64

	query := r.db.WithContext(ctx).Model(&models.BrandIdentity{})

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	err := query.
		Scopes(utils.Paginate(params)).
		Order("effective_at DESC").
		Find(&identities).Error

	if err != nil {
		return nil, 0, err
	}

	return identities, count, nil
}

func (r *brandIdentityRepository) Update(ctx context.Context, identity *models.BrandIdentity) error {
	return r.db.WithContext(ctx).Model(identity).Updates(identity).Error
}

func (r *brandIdentityRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.BrandIdentity{}, id).Error
}
