package repositories

import (
	"pvs-api/internal/dto"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type AuthRepository interface {
	GenerateToken(userID uint, username string, roleCode string, permissions []string, secret string) (string, error)
	ValidateToken(tokenString string, secret string) (*dto.CustomClaims, error)
}

type authRepository struct {
}

func NewAuthRepository() AuthRepository {
	return &authRepository{}
}

func (r *authRepository) GenerateToken(userID uint, username string, roleCode string, permissions []string, secret string) (string, error) {
	claims := dto.CustomClaims{
		UserID:      userID,
		Username:    username,
		RoleCode:    roleCode,
		Permissions: permissions,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

func (r *authRepository) ValidateToken(tokenString string, secret string) (*dto.CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &dto.CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*dto.CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.ErrTokenUnverifiable
}
