package repositories

import (
	"context"

	"pvs-api/internal/models"
	"pvs-api/pkg/utils"

	"gorm.io/gorm"
)

type RoleRepository interface {
	Create(ctx context.Context, role *models.Role) error
	GetByCode(ctx context.Context, code string) (*models.Role, error)
	List(ctx context.Context, params *utils.Pagination) ([]*models.Role, int64, error)
	Update(ctx context.Context, role *models.Role) error
	Delete(ctx context.Context, code string) error
	ListPermissionsByRole(ctx context.Context, roleCode string) ([]string, error)
	UpdatePermissions(ctx context.Context, roleCode string, permissions []string) error
}

type roleRepository struct {
	db *gorm.DB
}

func NewRoleRepository(db *gorm.DB) RoleRepository {
	return &roleRepository{db: db}
}

func (r *roleRepository) Create(ctx context.Context, role *models.Role) error {
	return r.db.WithContext(ctx).Create(role).Error
}

func (r *roleRepository) GetByCode(ctx context.Context, code string) (*models.Role, error) {
	var role models.Role
	err := r.db.WithContext(ctx).
		Where("code = ?", code).
		First(&role).Error
	if err != nil {
		return nil, err
	}
	return &role, nil
}

func (r *roleRepository) List(ctx context.Context, params *utils.Pagination) ([]*models.Role, int64, error) {
	var roles []*models.Role
	var count int64

	query := r.db.WithContext(ctx).Model(&models.Role{})

	// Apply filters if any
	if params.Filters != nil {
		if isSystem, ok := params.Filters["is_system"].(bool); ok {
			query = query.Where("is_system = ?", isSystem)
		}
	}

	// Count total records
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	err := query.
		Scopes(utils.Paginate(params)).
		Find(&roles).Error

	if err != nil {
		return nil, 0, err
	}

	return roles, count, nil
}

func (r *roleRepository) Update(ctx context.Context, role *models.Role) error {
	return r.db.WithContext(ctx).Model(role).Updates(role).Error
}

func (r *roleRepository) Delete(ctx context.Context, code string) error {
	return r.db.WithContext(ctx).Where("code = ?", code).Delete(&models.Role{}).Error
}

func (r *roleRepository) ListPermissionsByRole(ctx context.Context, roleCode string) ([]string, error) {
	var role models.Role
	err := r.db.WithContext(ctx).
		Where("code = ?", roleCode).
		First(&role).Error
	if err != nil {
		return nil, err
	}
	return role.Permissions, nil
}

func (r *roleRepository) UpdatePermissions(ctx context.Context, roleCode string, permissions []string) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var role models.Role
		if err := tx.Where("code = ?", roleCode).First(&role).Error; err != nil {
			return err
		}

		// Update permissions
		role.Permissions = permissions
		return tx.Save(&role).Error
	})
}
