package routes

import (
	"pvs-api/internal/api/middlewares"
	"pvs-api/internal/config"
	"pvs-api/internal/controllers"
	"pvs-api/internal/repositories"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes thiết lập tất cả các routes API
func SetupRoutes(router *gin.Engine, db *gorm.DB, jwtConfig *config.JWTConfig) {
	router.Static("/assets", "./assets") // Thêm đường dẫn đến thư mục chứa các tài nguyên tĩnh

	// Public routes
	public := router.Group("/api")
	{
		authController := controllers.NewAuthController(db, jwtConfig.Secret)
		public.POST("/login", authController.Login)
		public.POST("/refresh-token", authController.RefreshToken)

		// Swagger documentation - public access
		public.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.PersistAuthorization(true)))
	}

	// Private routes - require authentication
	api := router.Group("/api")
	api.Use(middlewares.AuthMiddleware(jwtConfig.Secret, repositories.NewAuthRepository())) // Apply JWT middleware to all routes in this group
	{
		// Brand Identities
		brandIdentityController := controllers.NewBrandIdentityController(db)
		brandIdentities := api.Group("/brand-identities")
		{
			brandIdentities.GET("", brandIdentityController.ListBrandIdentities)
			brandIdentities.GET("/active", brandIdentityController.GetActiveBrandIdentity)
			brandIdentities.GET("/:id", brandIdentityController.GetBrandIdentityByID)
			brandIdentities.POST("", brandIdentityController.CreateBrandIdentity)
			brandIdentities.DELETE("/:id", brandIdentityController.DeleteBrandIdentity)
		}

		// Notable Systems
		notableSystemController := controllers.NewNotableSystemController(db)
		notableSystems := api.Group("/notable-systems")
		{
			notableSystems.GET("", notableSystemController.ListNotableSystems)
			notableSystems.GET("/:id", notableSystemController.GetNotableSystemByID)
			notableSystems.POST("", notableSystemController.CreateNotableSystem)
			notableSystems.PUT("/:id", notableSystemController.UpdateNotableSystem)
			notableSystems.DELETE("/:id", notableSystemController.DeleteNotableSystem)
		}

		// Product Groups
		productGroupController := controllers.NewProductGroupController(db)
		productGroups := api.Group("/product-groups")
		{
			productGroups.GET("", productGroupController.ListProductGroups)
			productGroups.GET("/:id", productGroupController.GetProductGroupByID)
			productGroups.POST("", productGroupController.CreateProductGroup)
			productGroups.PUT("/:id", productGroupController.UpdateProductGroup)
			productGroups.DELETE("/:id", productGroupController.DeleteProductGroup)
			productGroups.PATCH("/:id/favorite", productGroupController.ToggleProductGroupFavorite)
			productGroups.GET("/:id/products", productGroupController.ListProductsByGroupID)
			productGroups.GET("/dropdown", productGroupController.ListForDropdown)
		}

		// Products
		productController := controllers.NewProductController(db)
		products := api.Group("/products")
		{
			products.GET("", productController.ListProducts)
			products.GET("/grouped", productController.ListGroupedProducts)
			products.GET("/:id", productController.GetProductByID)
			products.POST("", productController.CreateProduct)
			products.PUT("/:id", productController.UpdateProduct)
			products.DELETE("/:id", productController.DeleteProduct)
			products.GET("/:id/product-groups", productController.ListProductGroups)
			products.GET("/:id/components", productController.ListComponents)
			products.GET("/:id/stages", productController.ListStages)
			products.PATCH("/:id/status", productController.ChangeProductStatus)
		}

		// Product Journeys
		productJourneyController := controllers.NewProductJourneyController(db)
		productJourneys := api.Group("/product-journeys")
		{
			productJourneys.GET("", productJourneyController.ListProductJourneys)
			productJourneys.GET("/:id", productJourneyController.GetProductJourneyByID)
			productJourneys.POST("", productJourneyController.CreateProductJourney)
			productJourneys.PUT("/:id", productJourneyController.UpdateProductJourney)
			productJourneys.DELETE("/:id", productJourneyController.DeleteProductJourney)
			products.PATCH("/:id/favorite", productController.ToggleProductFavorite)
			products.GET("/:id/status-logs", productController.ListProductStatusLogs)
			products.GET("/:id/stage", productController.GetLatestStage)
			products.POST("/:id/set-stage", productController.SetProductStage)

			// Thêm/xóa sản phẩm vào/khỏi nhóm sản phẩm
			products.POST("/:id/product-groups/:group_id", productController.AddProductToGroup)
			products.DELETE("/:id/product-groups/:group_id", productController.RemoveProductFromGroup)

			// Thêm/xóa thành phần cho sản phẩm
			products.POST("/:id/components/:component_id", productController.AddComponentToProduct)
			products.DELETE("/:id/components/:component_id", productController.RemoveComponentFromProduct)
		}

		// Components
		componentController := controllers.NewComponentController(db)
		components := api.Group("/components")
		{
			components.GET("", componentController.ListComponents)
			components.GET("/:id", componentController.GetComponentByID)
			components.POST("", componentController.CreateComponent)
			components.PUT("/:id", componentController.UpdateComponent)
			components.DELETE("/:id", componentController.DeleteComponent)
			components.GET("/dropdown", componentController.ListForDropdown)
		}

		// Product Stages
		productStageController := controllers.NewProductStageController(db)
		productStages := api.Group("/product-stages")
		{
			productStages.GET("", productStageController.GetAllProductStages)
			productStages.GET("/:id", productStageController.GetProductStageByID)
			productStages.POST("", productStageController.CreateProductStage)
			productStages.PUT("/:id", productStageController.UpdateProductStage)
			productStages.DELETE("/:id", productStageController.DeleteProductStage)
			productStages.GET("/:id/stakeholders", productStageController.GetProductStageWithStakeholders)
			productStages.POST("/:id/stakeholders", productStageController.AddStakeholderToProductStage)
			productStages.DELETE("/:id/stakeholders/:stakeholder_id", productStageController.RemoveStakeholderFromProductStage)
		}

		// Stakeholders
		stakeholderController := controllers.NewStakeholderController(db)
		stakeholders := api.Group("/stakeholders")
		{
			stakeholders.GET("", stakeholderController.GetAllStakeholders)
			stakeholders.GET("/:id", stakeholderController.GetStakeholderByID)
			stakeholders.POST("", stakeholderController.CreateStakeholder)
			stakeholders.PUT("/:id", stakeholderController.UpdateStakeholder)
			stakeholders.DELETE("/:id", stakeholderController.DeleteStakeholder)
		}

		// System Parameters
		systemParamController := controllers.NewSystemParamController(db)
		systemParams := api.Group("/system-params")
		{
			systemParams.GET("", systemParamController.GetAllSystemParams)
			systemParams.GET("/types", systemParamController.GetAllParamTypes)
			systemParams.GET("/:id", systemParamController.GetSystemParamByID)
			systemParams.POST("", systemParamController.CreateSystemParam)
			systemParams.PUT("/:id", systemParamController.UpdateSystemParam)
			systemParams.DELETE("/:id", systemParamController.DeleteSystemParam)
		}

		// Users
		userController := controllers.NewUserController(db)
		users := api.Group("/users")
		{
			users.GET("", userController.ListUsers)
			users.GET("/:id", userController.GetUserByID)
			users.POST("", userController.CreateUser)
			users.PUT("/:id", userController.UpdateUser)
			users.PATCH("/:id/password", userController.ChangePassword)
			users.PATCH("/:id/role", userController.UpdateUserRole)
		}

		// Roles
		roleController := controllers.NewRoleController(db)
		roles := api.Group("/roles")
		{
			roles.GET("", roleController.ListRoles)
			roles.GET("/:code", roleController.GetRoleByCode)
			roles.POST("", roleController.CreateRole)
			roles.PUT("/:code", roleController.UpdateRole)
			roles.DELETE("/:code", roleController.DeleteRole)
			roles.PATCH("/:code/permissions", roleController.UpdateRolePermissions)
		}

		// Timeline
		timelineController := controllers.NewTimelineController(db)
		api.GET("/timeline", timelineController.GetTimeline)

		// Product Usage Stats
		productUsageController := controllers.NewProductUsageController(db)
		productUsage := api.Group("/product-usage")
		{
			productUsage.POST("/stats", productUsageController.UpdateUsageStat)
			productUsage.GET("/products/:product_id/stats", productUsageController.GetProductStats)
			productUsage.GET("/monthly-stats", productUsageController.GetMonthlyStats)
			productUsage.GET("/multi-products/stats", productUsageController.GetMultiProductStats)
			productUsage.GET("/all-products/stats", productUsageController.GetAllProductStats)
		}
	}
}
