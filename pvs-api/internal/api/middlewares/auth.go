package middlewares

import (
	"net/http"
	"pvs-api/internal/repositories"
	"pvs-api/pkg/utils"
	"strings"

	"github.com/gin-gonic/gin"
)

func AuthMiddleware(jwtSecret string, authRepo repositories.AuthRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" || !strings.HasPrefix(authHeader, "Bearer ") {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization required", "Missing or invalid authorization header")
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		claims, err := authRepo.ValidateToken(tokenString, jwtSecret)
		if err != nil {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Invalid token", err.Error())
			c.Abort()
			return
		}

		// Set claims to context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("roleCode", claims.RoleCode)
		c.Set("permissions", claims.Permissions)

		c.Next()
	}
}

func RequirePermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userPerms, exists := c.Get("permissions")
		if !exists {
			utils.ErrorResponse(c, http.StatusUnauthorized, "Authorization required", "User permissions not found")
			c.Abort()
			return
		}

		userPermissions := userPerms.([]string)
		hasPermission := false
		for _, required := range permissions {
			for _, userPerm := range userPermissions {
				if required == userPerm {
					hasPermission = true
					break
				}
			}
			if hasPermission {
				break
			}
		}

		if !hasPermission {
			utils.ErrorResponse(c, http.StatusForbidden, "Authorization required", "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}
