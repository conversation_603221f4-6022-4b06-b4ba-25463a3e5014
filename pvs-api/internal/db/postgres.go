package db

import (
	"fmt"

	"pvs-api/internal/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// SetupDB initializes the database connection and runs migrations
func SetupDB(config *config.DatabaseConfig) (*gorm.DB, error) {
	// Connect using GORM
	db, err := gorm.Open(postgres.Open(config.DSN()), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	fmt.Println("Connected to database")

	return db, nil
}
