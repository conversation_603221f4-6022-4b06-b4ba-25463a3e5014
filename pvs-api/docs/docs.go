// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "API Support",
            "email": "<EMAIL>"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/brand-identities": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tr<PERSON> về danh sách các lần thay đổi nhận diện thương hiệu",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "<PERSON><PERSON>y lịch sử thay đ<PERSON>i nhận diện thương hiệu",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một nhận diện thương hiệu mới và tự động cập nhật thời gian hết hiệu lực của nhận diện cũ",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "Tạo nhận diện thương hiệu mới",
                "parameters": [
                    {
                        "description": "Thông tin nhận diện thương hiệu",
                        "name": "identity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/brand-identities/active": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin nhận diện thương hiệu đang có hiệu lực",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "Lấy thông tin nhận diện thương hiệu hiện tại",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/brand-identities/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một nhận diện thương hiệu theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "Lấy chi tiết một nhận diện thương hiệu",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhận diện thương hiệu",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một nhận diện thương hiệu",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "Cập nhật nhận diện thương hiệu",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhận diện thương hiệu",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin nhận diện thương hiệu",
                        "name": "identity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.BrandIdentityResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một nhận diện thương hiệu khỏi hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Brand-Identity"
                ],
                "summary": "Xóa nhận diện thương hiệu",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhận diện thương hiệu",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/components": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả thành phần hệ thống có phân trang",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Lấy danh sách tất cả thành phần hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Số trang",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Số lượng mỗi trang",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Lọc theo parent ID",
                        "name": "parent_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.ComponentResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một thành phần hệ thống mới với thông tin được cung cấp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Tạo thành phần hệ thống mới",
                "parameters": [
                    {
                        "description": "Thông tin thành phần",
                        "name": "component",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.Component"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.Component"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/components/dropdown": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách id và tên của components, có thể lọc theo từ khóa",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Lấy danh sách component cho dropdown",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Search keyword",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.DropdownItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/components/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một thành phần hệ thống theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Lấy thông tin một thành phần hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID thành phần",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.Component"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một thành phần hệ thống theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Cập nhật thành phần hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID thành phần",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin thành phần",
                        "name": "component",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.Component"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.Component"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một thành phần hệ thống theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Xóa thành phần hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID thành phần",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/components/{id}/children": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các thành phần con của một thành phần hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Components"
                ],
                "summary": "Lấy danh sách các thành phần con",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID thành phần cha",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.Component"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "description": "Xác thực người dùng và trả về JWT token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Đăng nhập vào hệ thống",
                "parameters": [
                    {
                        "description": "Thông tin đăng nhập",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Đăng nhập thành công",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.AuthResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Lỗi dữ liệu đầu vào",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Thông tin đăng nhập không hợp lệ",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Lỗi server",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/notable-systems": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các hệ thống nổi bật đã triển khai",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notable-Systems"
                ],
                "summary": "Lấy danh sách các hệ thống nổi bật",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Lọc theo năm",
                        "name": "year",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một hệ thống nổi bật mới",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notable-Systems"
                ],
                "summary": "Tạo hệ thống nổi bật mới",
                "parameters": [
                    {
                        "description": "Thông tin hệ thống",
                        "name": "system",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/notable-systems/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một hệ thống nổi bật theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notable-Systems"
                ],
                "summary": "Lấy chi tiết một hệ thống nổi bật",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một hệ thống nổi bật",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notable-Systems"
                ],
                "summary": "Cập nhật hệ thống nổi bật",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin hệ thống",
                        "name": "system",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.NotableSystemResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một hệ thống nổi bật khỏi hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Notable-Systems"
                ],
                "summary": "Xóa hệ thống nổi bật",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-groups": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả nhóm sản phẩm trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Lấy danh sách tất cả nhóm sản phẩm",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một nhóm sản phẩm mới với thông tin được cung cấp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Tạo nhóm sản phẩm mới",
                "parameters": [
                    {
                        "description": "Thông tin nhóm sản phẩm",
                        "name": "productGroup",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductGroupRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductGroup"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-groups/dropdown": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách id và tên của product groups, có thể lọc theo từ khóa",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Lấy danh sách product group cho dropdown",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Search keyword",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.DropdownItem"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-groups/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một nhóm sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Lấy thông tin một nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_models.ProductGroup"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một nhóm sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Cập nhật nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin nhóm sản phẩm",
                        "name": "productGroup",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductGroup"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductGroup"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một nhóm sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Xóa nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-groups/{id}/products": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các sản phẩm thuộc nhóm sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Groups"
                ],
                "summary": "Lấy các sản phẩm liên quan nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.Product"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-stages": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả giai đoạn sản phẩm trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Lấy danh sách tất cả giai đoạn sản phẩm",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một giai đoạn sản phẩm mới trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Tạo giai đoạn sản phẩm mới",
                "parameters": [
                    {
                        "description": "Thông tin giai đoạn sản phẩm",
                        "name": "productStage",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductStageRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-stages/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một giai đoạn sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Lấy thông tin giai đoạn sản phẩm theo ID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một giai đoạn sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Cập nhật thông tin giai đoạn sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin giai đoạn sản phẩm",
                        "name": "productStage",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductStageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một giai đoạn sản phẩm khỏi hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Xóa giai đoạn sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-stages/{id}/stakeholders": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các người phụ trách tham gia vào giai đoạn sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Lấy danh sách người phụ trách của giai đoạn sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.ProductStageStakeholder"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thêm một người phụ trách vào giai đoạn sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Thêm người phụ trách vào giai đoạn sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin người phụ trách",
                        "name": "stakeholder",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductStageStakeholderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-stages/{id}/stakeholders/{stakeholder_id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một người phụ trách khỏi giai đoạn sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Stages"
                ],
                "summary": "Xóa người phụ trách khỏi giai đoạn sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID giai đoạn sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "ID người phụ trách",
                        "name": "stakeholder_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-usage/all-products/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Lấy số liệu người dùng của tất cả sản phẩm trong khoảng thời gian",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Usage"
                ],
                "summary": "Lấy số liệu tất cả sản phẩm theo thời gian",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Tháng bắt đầu (YYYY-MM)",
                        "name": "start_month",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Tháng kết thúc (YYYY-MM)",
                        "name": "end_month",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/internal_controllers.MultiProductStatsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-usage/monthly-stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Lấy số liệu người dùng của tất cả sản phẩm trong một tháng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Usage"
                ],
                "summary": "Lấy số liệu người dùng theo tháng",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Tháng (YYYY-MM)",
                        "name": "month",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "allOf": [
                                    {
                                        "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                    },
                                    {
                                        "type": "object",
                                        "properties": {
                                            "data": {
                                                "type": "array",
                                                "items": {
                                                    "$ref": "#/definitions/pvs-api_internal_models.ProductUsageStat"
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-usage/multi-products/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Lấy số liệu người dùng của nhiều sản phẩm trong khoảng thời gian",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Usage"
                ],
                "summary": "Lấy số liệu nhiều sản phẩm theo thời gian",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Danh sách ID sản phẩm, phân cách bởi dấu phẩy (vd: 1,2,3)",
                        "name": "product_ids",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Ngày bắt đầu (YYYY-MM-DD)",
                        "name": "start_date",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Ngày kết thúc (YYYY-MM-DD)",
                        "name": "end_date",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/internal_controllers.MultiProductStatsResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-usage/products/{product_id}/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Lấy số liệu người dùng của một sản phẩm trong khoảng thời gian",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Usage"
                ],
                "summary": "Lấy số liệu người dùng theo sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Ngày bắt đầu (YYYY-MM-DD)",
                        "name": "start_date",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Ngày kết thúc (YYYY-MM-DD)",
                        "name": "end_date",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "allOf": [
                                    {
                                        "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                    },
                                    {
                                        "type": "object",
                                        "properties": {
                                            "data": {
                                                "type": "array",
                                                "items": {
                                                    "$ref": "#/definitions/pvs-api_internal_models.ProductUsageStat"
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/product-usage/stats": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật số lượng người dùng cho một sản phẩm trong tháng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product-Usage"
                ],
                "summary": "Cập nhật số liệu người dùng sản phẩm",
                "parameters": [
                    {
                        "description": "Thông tin số liệu",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductUsageStat"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_models.ProductUsageStat"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả sản phẩm trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy danh sách tất cả sản phẩm",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.ProductResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một sản phẩm mới với thông tin được cung cấp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Tạo sản phẩm mới",
                "parameters": [
                    {
                        "description": "Thông tin sản phẩm",
                        "name": "product",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.ProductResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/grouped": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các group và sản phẩm thuộc group đó, có thể lọc theo group, trạng thái và giai đoạn hiện tại",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy danh sách sản phẩm được nhóm theo group có phân trang",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Số trang",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Số lượng sản phẩm mỗi trang",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "ID của group cần lọc",
                        "name": "group_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "ID của component cần lọc",
                        "name": "component_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Năm cần lọc",
                        "name": "year",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Trạng thái cần lọc",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.GroupedProductsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy thông tin một sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.ProductResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Cập nhật sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin sản phẩm",
                        "name": "product",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ProductRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.ProductResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một sản phẩm theo ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Xóa sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/components": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một sản phẩm và danh sách các thành phần hệ thống liên quan",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy sản phẩm và các thành phần liên quan",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.Component"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/components/{component_id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thêm một thành phần hệ thống vào một sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Thêm thành phần vào sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "ID thành phần",
                        "name": "component_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một thành phần hệ thống khỏi một sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Xóa thành phần khỏi sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "ID thành phần",
                        "name": "component_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/product-groups": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách các nhóm sản phẩm mà sản phẩm thuộc về",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy các nhóm sản phẩm liên quan",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.ProductDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/product-groups/{group_id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thêm một sản phẩm vào một nhóm sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Thêm sản phẩm vào nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một sản phẩm khỏi một nhóm sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Xóa sản phẩm khỏi nhóm sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "ID nhóm sản phẩm",
                        "name": "group_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/set-stage": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thiết lập giai đoạn mới cho sản phẩm và cập nhật giai đoạn hiện tại của sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Thiết lập giai đoạn mới cho sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin giai đoạn mới",
                        "name": "stage",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.SetProductStageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/stage": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về giai đoạn hiện tại cùng của sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy giai đoạn hiện tại của sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/stages": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về lịch sử các giai đoạn của sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy thông tin các giai đoạn",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/status": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thay đổi trạng thái active/inactive của sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Thay đổi trạng thái sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Trạng thái sản phẩm",
                        "name": "status",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/products/{id}/status-logs": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về lịch sử các thay đổi trạng thái của sản phẩm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Lấy lịch sử trạng thái sản phẩm",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID sản phẩm",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/pvs-api_internal_models.ProductStatusLog"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/refresh-token": {
            "post": {
                "description": "Tạo token mới dựa trên token hiện tại",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Làm mới token",
                "parameters": [
                    {
                        "description": "Token hiện tại",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RefreshTokenRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Token mới",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.AuthResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Lỗi dữ liệu đầu vào",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Token không hợp lệ hoặc người dùng không tồn tại",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Lỗi server",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/roles": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả vai trò có phân trang",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Lấy danh sách vai trò",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Số trang",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Số lượng mỗi trang",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.RoleResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một vai trò mới với thông tin được cung cấp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Tạo vai trò mới",
                "parameters": [
                    {
                        "description": "Thông tin vai trò",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RoleRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RoleResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/roles/{code}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một vai trò theo mã",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Lấy thông tin vai trò theo mã",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Mã vai trò",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RoleResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một vai trò theo mã",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Cập nhật thông tin vai trò",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Mã vai trò",
                        "name": "code",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin vai trò",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RoleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.RoleResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "403": {
                        "description": "Forbidden - Cannot modify system role",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một vai trò theo mã",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Xóa vai trò",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Mã vai trò",
                        "name": "code",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request - Role is assigned to users",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "403": {
                        "description": "Forbidden - Cannot delete system role",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/roles/{code}/permissions": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật danh sách quyền cho một vai trò theo mã",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Roles"
                ],
                "summary": "Cập nhật quyền cho vai trò",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Mã vai trò",
                        "name": "code",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Danh sách quyền mới",
                        "name": "permissions",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.UpdateRolePermissionsRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "403": {
                        "description": "Forbidden - Cannot modify system role",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/stakeholders": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tất cả người phụ trách trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Stakeholders"
                ],
                "summary": "Lấy danh sách tất cả người phụ trách",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một người phụ trách mới trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Stakeholders"
                ],
                "summary": "Tạo người phụ trách mới",
                "parameters": [
                    {
                        "description": "Thông tin người phụ trách",
                        "name": "stakeholder",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.StakeholderRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.StakeholderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/stakeholders/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một người phụ trách",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Stakeholders"
                ],
                "summary": "Lấy thông tin người phụ trách theo ID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID người phụ trách",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.StakeholderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một người phụ trách",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Stakeholders"
                ],
                "summary": "Cập nhật thông tin người phụ trách",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID người phụ trách",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin người phụ trách",
                        "name": "stakeholder",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.StakeholderRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.StakeholderResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một người phụ trách khỏi hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Stakeholders"
                ],
                "summary": "Xóa người phụ trách",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID người phụ trách",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/system-params": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách tham số hệ thống có phân trang",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System-Params"
                ],
                "summary": "Lấy danh sách tham số hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Số trang",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Số lượng mỗi trang",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Lọc theo loại tham số",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Lọc theo trạng thái kích hoạt",
                        "name": "is_active",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/pvs-api_internal_dto.SystemParamResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một tham số hệ thống mới với thông tin được cung cấp",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System-Params"
                ],
                "summary": "Tạo tham số hệ thống mới",
                "parameters": [
                    {
                        "description": "Thông tin tham số hệ thống",
                        "name": "systemParam",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.SystemParamRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.SystemParamResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/system-params/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một tham số hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System-Params"
                ],
                "summary": "Lấy thông tin tham số hệ thống theo ID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID tham số hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.SystemParamResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một tham số hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System-Params"
                ],
                "summary": "Cập nhật tham số hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID tham số hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin tham số hệ thống",
                        "name": "systemParam",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.SystemParamRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.SystemParamResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Xóa một tham số hệ thống khỏi hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System-Params"
                ],
                "summary": "Xóa tham số hệ thống",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "ID tham số hệ thống",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/timeline": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Returns a timeline view of all product groups with their products, stages, status logs, and components",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Timeline"
                ],
                "summary": "Get product timeline",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_dto.TimelineResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về danh sách người dùng có phân trang",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Lấy danh sách người dùng",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Số trang",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Số lượng mỗi trang",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Lọc theo trạng thái kích hoạt",
                        "name": "is_active",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Lọc theo role code",
                        "name": "role_code",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Tạo một người dùng mới trong hệ thống",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Tạo người dùng mới",
                "parameters": [
                    {
                        "description": "Thông tin người dùng",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.CreateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/users/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Trả về thông tin chi tiết của một người dùng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Lấy thông tin người dùng theo ID",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật thông tin của một người dùng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Cập nhật thông tin người dùng",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin người dùng",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.UpdateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/pvs-api_internal_models.User"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/users/{id}/password": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Cập nhật mật khẩu của một người dùng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Đổi mật khẩu người dùng",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin mật khẩu",
                        "name": "passwords",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.ChangePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        },
        "/users/{id}/role": {
            "patch": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Thay đổi role của một người dùng",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Users"
                ],
                "summary": "Cập nhật role của người dùng",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "User ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Thông tin role",
                        "name": "role",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pvs-api_internal_dto.UpdateUserRoleRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/pvs-api_pkg_utils.Response"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "gorm.DeletedAt": {
            "type": "object",
            "properties": {
                "time": {
                    "type": "string"
                },
                "valid": {
                    "description": "Valid is true if Time is not NULL",
                    "type": "boolean"
                }
            }
        },
        "internal_controllers.MultiProductStatsResponse": {
            "type": "object",
            "properties": {
                "datasets": {
                    "description": "Dữ liệu của từng sản phẩm",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/internal_controllers.ProductDataset"
                    }
                },
                "labels": {
                    "description": "Các mốc thời gian",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "internal_controllers.ProductDataset": {
            "type": "object",
            "properties": {
                "data": {
                    "description": "Số liệu theo thời gian",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "product_id": {
                    "type": "integer"
                },
                "product_name": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.AuthResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                },
                "user": {
                    "$ref": "#/definitions/pvs-api_internal_dto.UserInfo"
                }
            }
        },
        "pvs-api_internal_dto.BrandIdentityRequest": {
            "type": "object",
            "required": [
                "color_code",
                "company_name",
                "effective_at"
            ],
            "properties": {
                "color_code": {
                    "type": "string",
                    "example": "#FF0000"
                },
                "company_name": {
                    "type": "string",
                    "example": "Acme Corp"
                },
                "effective_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "expired_at": {
                    "type": "string",
                    "example": "2024-12-31T23:59:59Z"
                },
                "logo_url": {
                    "type": "string",
                    "example": "https://example.com/logo.png"
                },
                "note": {
                    "type": "string",
                    "example": "Rebranding for 2024"
                }
            }
        },
        "pvs-api_internal_dto.BrandIdentityResponse": {
            "type": "object",
            "properties": {
                "color_code": {
                    "type": "string",
                    "example": "#FF0000"
                },
                "company_name": {
                    "type": "string",
                    "example": "Acme Corp"
                },
                "created_at": {
                    "type": "string"
                },
                "effective_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "expired_at": {
                    "type": "string",
                    "example": "2024-12-31T23:59:59Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "is_active": {
                    "type": "boolean",
                    "example": true
                },
                "logo_url": {
                    "type": "string",
                    "example": "https://example.com/logo.png"
                },
                "note": {
                    "type": "string",
                    "example": "Rebranding for 2024"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ChangePasswordRequest": {
            "type": "object",
            "required": [
                "new_password",
                "old_password"
            ],
            "properties": {
                "new_password": {
                    "type": "string",
                    "minLength": 6
                },
                "old_password": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ComponentResponse": {
            "type": "object",
            "properties": {
                "component_type_code": {
                    "type": "string",
                    "example": "APPLICATION"
                },
                "confluence_url": {
                    "type": "string",
                    "example": "https://confluence.example.com/pages/component/123"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "example": "Dịch vụ xác thực người dùng"
                },
                "extra_function": {
                    "type": "string"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "importance": {
                    "type": "string"
                },
                "main_function": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "example": "Authentication Service"
                },
                "parent_component_id": {
                    "type": "integer",
                    "example": 0
                },
                "target_user_code": {
                    "type": "string",
                    "example": "CUSTOMER"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.CreateUserRequest": {
            "type": "object",
            "required": [
                "email",
                "full_name",
                "password",
                "role_code",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "role_code": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.DropdownItem": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "Example Item"
                }
            }
        },
        "pvs-api_internal_dto.GroupWithProducts": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.ProductInGroup"
                    }
                }
            }
        },
        "pvs-api_internal_dto.GroupedProductsResponse": {
            "type": "object",
            "properties": {
                "count": {
                    "description": "Số lượng sản phẩm trong trang hiện tại",
                    "type": "integer"
                },
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.GroupWithProducts"
                    }
                },
                "total": {
                    "description": "Tổng số sản phẩm thỏa mãn điều kiện",
                    "type": "integer"
                }
            }
        },
        "pvs-api_internal_dto.LoginRequest": {
            "type": "object",
            "required": [
                "password",
                "username"
            ],
            "properties": {
                "password": {
                    "type": "string",
                    "example": "Admin@123"
                },
                "username": {
                    "type": "string",
                    "example": "admin"
                }
            }
        },
        "pvs-api_internal_dto.NotableSystemRequest": {
            "type": "object",
            "required": [
                "name",
                "start_year"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn"
                },
                "end_year": {
                    "type": "integer",
                    "example": 2023
                },
                "name": {
                    "type": "string",
                    "example": "Core Banking System"
                },
                "start_year": {
                    "type": "integer",
                    "example": 2020
                }
            }
        },
        "pvs-api_internal_dto.NotableSystemResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Hệ thống ngân hàng lõi được triển khai cho nhiều ngân hàng lớn"
                },
                "end_year": {
                    "type": "integer",
                    "example": 2023
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "Core Banking System"
                },
                "start_year": {
                    "type": "integer",
                    "example": 2020
                }
            }
        },
        "pvs-api_internal_dto.ProductDetailResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "example": "Ứng dụng ngân hàng di động"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "Banking App"
                },
                "product_groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.ProductGroupResponse"
                    }
                },
                "status": {
                    "type": "string",
                    "example": "ACTIVE"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ProductGroupRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Nhóm các ứng dụng di động"
                },
                "name": {
                    "type": "string",
                    "example": "Mobile Apps"
                }
            }
        },
        "pvs-api_internal_dto.ProductGroupResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "example": "Nhóm các ứng dụng di động"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "Mobile Apps"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ProductInGroup": {
            "type": "object",
            "properties": {
                "current_stage": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "has_usage_data": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ProductRequest": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Ứng dụng ngân hàng di động"
                },
                "name": {
                    "type": "string",
                    "example": "Banking App"
                },
                "status": {
                    "type": "string",
                    "example": "ACTIVE"
                }
            }
        },
        "pvs-api_internal_dto.ProductResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string",
                    "example": "Ứng dụng ngân hàng di động"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "Banking App"
                },
                "status": {
                    "type": "string",
                    "example": "ACTIVE"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.ProductStageRequest": {
            "type": "object",
            "required": [
                "product_id",
                "stage_code",
                "start_date"
            ],
            "properties": {
                "end_date": {
                    "type": "string",
                    "example": "2023-02-01T00:00:00Z"
                },
                "notes": {
                    "type": "string",
                    "example": "Hoàn thành giai đoạn nghiên cứu ý tưởng"
                },
                "product_id": {
                    "type": "integer",
                    "example": 1
                },
                "stage_code": {
                    "type": "string",
                    "example": "IDEA"
                },
                "start_date": {
                    "type": "string",
                    "example": "2023-01-01T00:00:00Z"
                }
            }
        },
        "pvs-api_internal_dto.ProductStageStakeholderRequest": {
            "type": "object",
            "required": [
                "role_code",
                "stakeholder_id"
            ],
            "properties": {
                "role_code": {
                    "type": "string",
                    "example": "PRIMARY"
                },
                "stakeholder_id": {
                    "type": "integer"
                },
                "tasks": {
                    "type": "string",
                    "example": "Phụ trách quản lý tiến độ sprint"
                }
            }
        },
        "pvs-api_internal_dto.RefreshTokenRequest": {
            "type": "object",
            "required": [
                "token"
            ],
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "pvs-api_internal_dto.RoleRequest": {
            "type": "object",
            "required": [
                "code",
                "name"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "PRODUCT_MANAGER"
                },
                "description": {
                    "type": "string",
                    "example": "Manages product lifecycle"
                },
                "name": {
                    "type": "string",
                    "example": "Product Manager"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "['product:create'",
                        "'product:update']"
                    ]
                }
            }
        },
        "pvs-api_internal_dto.RoleResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "PRODUCT_MANAGER"
                },
                "description": {
                    "type": "string",
                    "example": "Manages product lifecycle"
                },
                "is_system": {
                    "type": "boolean",
                    "example": false
                },
                "name": {
                    "type": "string",
                    "example": "Product Manager"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "['product:create'",
                        "'product:update']"
                    ]
                }
            }
        },
        "pvs-api_internal_dto.SetProductStageRequest": {
            "type": "object",
            "required": [
                "stage_code",
                "start_date"
            ],
            "properties": {
                "notes": {
                    "type": "string",
                    "example": "Chuyển sang giai đoạn phát triển"
                },
                "stage_code": {
                    "type": "string",
                    "example": "DEVELOPMENT"
                },
                "start_date": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.StakeholderRequest": {
            "type": "object",
            "required": [
                "email",
                "full_name"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "full_name": {
                    "type": "string",
                    "example": "Nguyễn Văn A"
                },
                "position": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.StakeholderResponse": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "full_name": {
                    "type": "string",
                    "example": "Nguyễn Văn A"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.SystemParamRequest": {
            "type": "object",
            "required": [
                "code",
                "name",
                "param_type"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "ACTIVE"
                },
                "display_order": {
                    "type": "integer",
                    "example": 1
                },
                "is_active": {
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "type": "string",
                    "example": "Đang hoạt động"
                },
                "param_type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/pvs-api_internal_models.ParamType"
                        }
                    ],
                    "example": "PRODUCT_STATUS"
                }
            }
        },
        "pvs-api_internal_dto.SystemParamResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "example": "ACTIVE"
                },
                "created_at": {
                    "type": "string"
                },
                "display_order": {
                    "type": "integer",
                    "example": 1
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "is_active": {
                    "type": "boolean",
                    "example": true
                },
                "name": {
                    "type": "string",
                    "example": "Đang hoạt động"
                },
                "param_type": {
                    "allOf": [
                        {
                            "$ref": "#/definitions/pvs-api_internal_models.ParamType"
                        }
                    ],
                    "example": "PRODUCT_STATUS"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.TimelineComponentInfo": {
            "type": "object",
            "properties": {
                "link": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.TimelineGroup": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.TimelineProduct"
                    }
                }
            }
        },
        "pvs-api_internal_dto.TimelineProduct": {
            "type": "object",
            "properties": {
                "components": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.TimelineComponentInfo"
                    }
                },
                "end_year": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "logs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.TimelineStatusLog"
                    }
                },
                "name": {
                    "type": "string"
                },
                "stages": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.TimelineStage"
                    }
                },
                "start_year": {
                    "type": "integer"
                }
            }
        },
        "pvs-api_internal_dto.TimelineResponse": {
            "type": "object",
            "properties": {
                "end_year": {
                    "description": "Latest year across all products",
                    "type": "integer"
                },
                "groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_dto.TimelineGroup"
                    }
                },
                "start_year": {
                    "description": "Earliest year across all products",
                    "type": "integer"
                }
            }
        },
        "pvs-api_internal_dto.TimelineStage": {
            "type": "object",
            "properties": {
                "end_year": {
                    "description": "Optional end year",
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "start_year": {
                    "type": "integer"
                }
            }
        },
        "pvs-api_internal_dto.TimelineStatusLog": {
            "type": "object",
            "properties": {
                "end_year": {
                    "type": "integer"
                },
                "start_year": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.UpdateRolePermissionsRequest": {
            "type": "object",
            "required": [
                "permissions"
            ],
            "properties": {
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "pvs-api_internal_dto.UpdateUserRequest": {
            "type": "object",
            "required": [
                "email",
                "full_name"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                }
            }
        },
        "pvs-api_internal_dto.UpdateUserRoleRequest": {
            "type": "object",
            "required": [
                "role_code"
            ],
            "properties": {
                "role_code": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_dto.UserInfo": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "['user:list'",
                        " 'user:create']"
                    ]
                },
                "role_code": {
                    "type": "string",
                    "example": "ADMIN"
                },
                "username": {
                    "type": "string",
                    "example": "admin"
                }
            }
        },
        "pvs-api_internal_models.Component": {
            "type": "object",
            "properties": {
                "child_components": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.Component"
                    }
                },
                "component_type_code": {
                    "type": "string"
                },
                "confluence_url": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "extra_function": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "importance": {
                    "type": "string"
                },
                "main_function": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "parent_component": {
                    "$ref": "#/definitions/pvs-api_internal_models.Component"
                },
                "parent_component_id": {
                    "type": "integer"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.Product"
                    }
                },
                "target_user_code": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ParamType": {
            "type": "string",
            "enum": [
                "COMPONENT_TYPE",
                "TARGET_USER",
                "STAGE_TYPE",
                "PRODUCT_STATUS",
                "STAKEHOLDER_ROLE"
            ],
            "x-enum-comments": {
                "ParamTypeComponentType": "Loại hệ thống/thành phần",
                "ParamTypeProductStatus": "Trạng thái của sản phẩm",
                "ParamTypeStageType": "Giai đoạn của sản phẩm",
                "ParamTypeStakeholderRole": "Vai trò của người tham gia sản phẩm",
                "ParamTypeTargetUser": "Nhóm người dùng sử dụng hệ thống"
            },
            "x-enum-varnames": [
                "ParamTypeComponentType",
                "ParamTypeTargetUser",
                "ParamTypeStageType",
                "ParamTypeProductStatus",
                "ParamTypeStakeholderRole"
            ]
        },
        "pvs-api_internal_models.Product": {
            "type": "object",
            "properties": {
                "components": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.Component"
                    }
                },
                "created_at": {
                    "type": "string"
                },
                "current_stage": {
                    "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "product_groups": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.ProductGroup"
                    }
                },
                "product_stages": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                    }
                },
                "stage_id": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ProductGroup": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "products": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.Product"
                    }
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ProductStage": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "end_date": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "notes": {
                    "type": "string"
                },
                "product": {
                    "$ref": "#/definitions/pvs-api_internal_models.Product"
                },
                "product_id": {
                    "type": "integer"
                },
                "stage_code": {
                    "type": "string"
                },
                "stakeholders": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.ProductStageStakeholder"
                    }
                },
                "start_date": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ProductStageStakeholder": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "product_stage": {
                    "$ref": "#/definitions/pvs-api_internal_models.ProductStage"
                },
                "product_stage_id": {
                    "type": "integer"
                },
                "role_code": {
                    "type": "string"
                },
                "stakeholder": {
                    "$ref": "#/definitions/pvs-api_internal_models.Stakeholder"
                },
                "stakeholder_id": {
                    "type": "integer"
                },
                "tasks": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ProductStatusLog": {
            "type": "object",
            "properties": {
                "changed_at": {
                    "type": "string"
                },
                "changed_by": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "product": {
                    "$ref": "#/definitions/pvs-api_internal_models.Product"
                },
                "product_id": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.ProductUsageStat": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "month": {
                    "description": "Chỉ lưu năm-tháng",
                    "type": "string"
                },
                "product": {
                    "$ref": "#/definitions/pvs-api_internal_models.Product"
                },
                "product_id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "users": {
                    "type": "integer"
                }
            }
        },
        "pvs-api_internal_models.Role": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "is_system": {
                    "description": "Đánh dấu role hệ thống, không được xóa",
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "permissions": {
                    "description": "Mảng các permission codes",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "updated_at": {
                    "type": "string"
                },
                "users": {
                    "description": "Relations",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/pvs-api_internal_models.User"
                    }
                }
            }
        },
        "pvs-api_internal_models.Stakeholder": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "position": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "pvs-api_internal_models.User": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "is_active": {
                    "type": "boolean"
                },
                "last_login": {
                    "type": "string"
                },
                "role": {
                    "description": "Relations",
                    "allOf": [
                        {
                            "$ref": "#/definitions/pvs-api_internal_models.Role"
                        }
                    ]
                },
                "role_code": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "pvs-api_pkg_utils.Response": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "data": {},
                "error": {},
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                },
                "total": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "Xác thực bằng JWT Token với tiền tố Bearer. Ví dụ: \"Bearer {token}\"",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "/api",
	Schemes:          []string{},
	Title:            "Hệ thống Quản lý Sản phẩm API",
	Description:      "API cho Hệ thống Quản lý Sản phẩm sử dụng Gin và GORM",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
