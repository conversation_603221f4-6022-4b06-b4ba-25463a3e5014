# Product Management API

A RESTful API for managing products, product groups, components, and stakeholders built with Go, Gin, and GORM.

## Features

- Product management with grouping capabilities
- Component management with hierarchical structure
- Stakeholder management
- Product stage tracking
- RESTful API with Swagger documentation
- PostgreSQL database integration

## Tech Stack

- **Go** (v1.21+)
- **Gin** - Web framework
- **GORM** - ORM library for database operations
- **PostgreSQL** - Database
- **Swagger** - API documentation
- **Docker** - Containerization

## Prerequisites

- Go 1.21 or higher
- PostgreSQL
- Docker (optional)

## Getting Started

### Local Development

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd pvs-api
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

3. Install dependencies:
   ```bash
   go mod download
   ```

4. Run the application:
   ```bash
   go run cmd/main.go
   # or
   make run
   ```

5. Access the API at `http://localhost:8080/api`

6. Access Swagger documentation at `http://localhost:8080/swagger/index.html`

### Using Docker

1. Build and run with Docker:
   ```bash
   docker build -t pvs-api .
   docker run -p 8080:8080 --env-file .env pvs-api
   ```

## API Endpoints

The API provides the following endpoints:

- `/api/product-groups` - Manage product groups
- `/api/products` - Manage products
- `/api/components` - Manage components
- `/api/stakeholders` - Manage stakeholders

For detailed API documentation, refer to the Swagger documentation at `/swagger/index.html`.

## Project Structure

```
.
├── cmd/                  # Application entry points
│   └── main.go           # Main application
├── docs/                 # Swagger documentation
├── internal/             # Private application code
│   ├── api/              # API-related code
│   │   ├── middlewares/  # HTTP middlewares
│   │   └── routes/       # Route definitions
│   ├── config/           # Configuration
│   ├── controllers/      # HTTP controllers
│   ├── db/               # Database setup
│   │   └── migrations/   # SQL migration files
│   ├── dto/              # Data Transfer Objects
│   ├── models/           # Database models
│   └── repositories/     # Data access layer
├── pkg/                  # Public libraries
│   ├── logger/           # Logging utilities
│   └── utils/            # Utility functions
├── .env.example          # Example environment variables
├── Dockerfile            # Docker configuration
├── go.mod                # Go module definition
├── go.sum                # Go module checksums
├── Makefile              # Build automation
└── README.md             # Project documentation
```

## Development

### Generate Swagger Documentation

```bash
make swag-init
```

## License

[MIT License](LICENSE)
