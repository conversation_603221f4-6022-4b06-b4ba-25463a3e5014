# Dockerfile for React frontend application build process.
# This is a multi-stage build process. The first stage is the builder stage, which builds the application and the second stage is the production stage, which copies the built files to a production-ready Nginx image.
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install

# Copy source code
COPY . .

# Build the application
RUN pnpm run build


# Production stage
FROM nginx:1-alpine as production

# Copy the built files from the builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]