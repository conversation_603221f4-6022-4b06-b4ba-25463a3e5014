{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "axios": "^1.10.0", "bootstrap": "^5.3.7", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "styled-components": "^6.1.19", "uuid": "^11.1.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.37.0", "vite": "^7.0.5"}}