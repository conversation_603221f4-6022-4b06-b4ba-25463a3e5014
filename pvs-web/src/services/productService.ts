import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

// Types
export interface ProductInGroup {
  id: number;
  name: string;
  description: string;
  current_stage: string;
  status: string;
  is_favorite: boolean;
  has_usage_data: boolean;
}

export interface GroupWithProducts {
  id: number;
  name: string;
  description: string;
  is_favorite: boolean;
  products: ProductInGroup[];
}

export interface GroupedProductsResponse {
  groups: GroupWithProducts[];
  count: number;
  total: number;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface ProductFilters {
  page?: number;
  limit?: number;
  group_id?: number;
  year?: number;
  status?: string;
  is_favorite?: boolean;
  group_is_favorite?: boolean;
}

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const productService = {
  // Get grouped products with filters and pagination
  async getGroupedProducts(filters: ProductFilters = {}): Promise<GroupedProductsResponse> {
    try {
      const params = new URLSearchParams();

      // Add pagination params
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      // Add filter params (exclude group_is_favorite as it's handled client-side)
      if (filters.group_id) params.append('group_id', filters.group_id.toString());
      if (filters.year) params.append('year', filters.year.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.is_favorite !== undefined) params.append('is_favorite', filters.is_favorite.toString());

      const response = await apiClient.get<ApiResponse<GroupedProductsResponse>>(
        `/products/grouped?${params.toString()}`
      );

      if (response.data.success) {
        let result = response.data.data;

        // Apply client-side group_is_favorite filter
        if (filters.group_is_favorite !== undefined) {
          result.groups = result.groups.filter(group => group.is_favorite === filters.group_is_favorite);
        }

        return result;
      } else {
        throw new Error(response.data.message || 'Failed to fetch grouped products');
      }
    } catch (error) {
      console.error('Error fetching grouped products:', error);
      throw error;
    }
  },

  // Toggle product favorite status
  async toggleProductFavorite(productId: number, isFavorite: boolean): Promise<void> {
    try {
      const response = await apiClient.patch<ApiResponse<null>>(
        `/products/${productId}/favorite?is_favorite=${isFavorite}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to toggle product favorite');
      }
    } catch (error) {
      console.error('Error toggling product favorite:', error);
      throw error;
    }
  },

  // Toggle product group favorite status
  async toggleProductGroupFavorite(groupId: number, isFavorite: boolean): Promise<void> {
    try {
      const response = await apiClient.patch<ApiResponse<null>>(
        `/product-groups/${groupId}/favorite?is_favorite=${isFavorite}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to toggle product group favorite');
      }
    } catch (error) {
      console.error('Error toggling product group favorite:', error);
      throw error;
    }
  },

  // Get product groups for filter dropdown
  async getProductGroups(): Promise<{ id: number; name: string; description: string }[]> {
    try {
      const response = await apiClient.get<ApiResponse<{ id: number; name: string; description: string }[]>>(
        '/product-groups'
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch product groups');
      }
    } catch (error) {
      console.error('Error fetching product groups:', error);
      throw error;
    }
  },
};

export default productService;
