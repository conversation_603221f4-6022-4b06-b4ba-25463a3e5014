import axios from 'axios';
import { mockAuthService } from './mockAuthService';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';
const USE_MOCK = import.meta.env.VITE_USE_MOCK_API === 'true';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  role_code: string;
  permissions: string[];
}

export interface AuthResponse {
  token: string;
  user: UserInfo;
}

export const authService = {
  async login(username: string, password: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.login(username, password);
    }

    const response = await axios.post(`${API_URL}/login`, {
      username,
      password
    });
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error(response.data.message || 'Login failed');
  },

  async refreshToken(token: string): Promise<AuthResponse> {
    if (USE_MOCK) {
      return mockAuthService.refreshToken(token);
    }

    const response = await axios.post(`${API_URL}/refresh-token`, {
      token
    });
    
    if (response.data.success) {
      return response.data.data;
    }
    throw new Error(response.data.message || 'Token refresh failed');
  },

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  getCurrentUser(): UserInfo | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  getToken(): string | null {
    return localStorage.getItem('token');
  },

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
};
