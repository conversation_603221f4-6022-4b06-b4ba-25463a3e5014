import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

// Types
export interface ProductJourneyComponent {
  id: number;
  component_id: number;
  component_name?: string;
}

export interface ProductJourney {
  id?: number;
  name: string;
  description: string;
  product_id: number;
  product_name?: string;
  flow_data: string;
  components?: ProductJourneyComponent[];
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface ProductJourneyListItem {
  id: number;
  name: string;
  description: string;
  product_id: number;
  product_name: string;
  status: string;
  component_count: number;
  created_at: string;
  updated_at: string;
}

export interface ProductJourneyRequest {
  name: string;
  description: string;
  product_id: number;
  flow_data: string;
  components: {
    component_id: number;
  }[];
  status: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

export interface ProductJourneyFilters {
  page?: number;
  limit?: number;
  product_id?: number;
  status?: string;
  search?: string;
}

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const productJourneyService = {
  // Get product journeys with filters and pagination
  async getProductJourneys(filters: ProductJourneyFilters = {}): Promise<PaginatedResponse<ProductJourneyListItem>> {
    try {
      const params = new URLSearchParams();

      // Add pagination params
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      // Add filter params
      if (filters.product_id) params.append('product_id', filters.product_id.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.search) params.append('search', filters.search);

      const response = await apiClient.get<PaginatedResponse<ProductJourneyListItem>>(
        `/product-journeys?${params.toString()}`
      );

      if (response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch product journeys');
      }
    } catch (error) {
      console.error('Error fetching product journeys:', error);
      throw error;
    }
  },

  // Get product journey by ID
  async getProductJourneyById(id: number): Promise<ProductJourney> {
    try {
      const response = await apiClient.get<ApiResponse<ProductJourney>>(
        `/product-journeys/${id}`
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to fetch product journey');
      }
    } catch (error) {
      console.error('Error fetching product journey:', error);
      throw error;
    }
  },

  // Create new product journey
  async createProductJourney(journey: ProductJourneyRequest): Promise<ProductJourney> {
    try {
      const response = await apiClient.post<ApiResponse<ProductJourney>>(
        '/product-journeys',
        journey
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to create product journey');
      }
    } catch (error) {
      console.error('Error creating product journey:', error);
      throw error;
    }
  },

  // Update product journey
  async updateProductJourney(id: number, journey: ProductJourneyRequest): Promise<ProductJourney> {
    try {
      const response = await apiClient.put<ApiResponse<ProductJourney>>(
        `/product-journeys/${id}`,
        journey
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to update product journey');
      }
    } catch (error) {
      console.error('Error updating product journey:', error);
      throw error;
    }
  },

  // Delete product journey
  async deleteProductJourney(id: number): Promise<void> {
    try {
      const response = await apiClient.delete<ApiResponse<null>>(
        `/product-journeys/${id}`
      );

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to delete product journey');
      }
    } catch (error) {
      console.error('Error deleting product journey:', error);
      throw error;
    }
  },
};

export default productJourneyService;
