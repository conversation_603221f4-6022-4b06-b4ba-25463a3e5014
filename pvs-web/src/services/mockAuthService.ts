export interface LoginRequest {
  username: string;
  password: string;
}

export interface UserInfo {
  id: number;
  username: string;
  role_code: string;
  permissions: string[];
}

export interface AuthResponse {
  token: string;
  user: UserInfo;
}

// Mock user data
const mockUsers: Record<string, UserInfo> = {
  admin: {
    id: 1,
    username: 'admin',
    role_code: 'ADMIN',
    permissions: ['user:list', 'user:create', 'user:update', 'user:delete', 'role:manage']
  },
  user: {
    id: 2,
    username: 'user',
    role_code: 'USER',
    permissions: ['user:view', 'dashboard:view']
  },
  viewer: {
    id: 3,
    username: 'viewer',
    role_code: 'VIEWER',
    permissions: ['dashboard:view']
  }
};

// Generate a mock JWT token
function generateMockToken(user: UserInfo): string {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    user_id: user.id,
    username: user.username,
    role_code: user.role_code,
    permissions: user.permissions,
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  }));
  const signature = btoa('mock-signature');
  
  return `${header}.${payload}.${signature}`;
}

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const mockAuthService = {
  async login(username: string, password: string): Promise<AuthResponse> {
    // Simulate API delay
    await delay(500);
    
    // Check if username and password are the same
    if (username !== password) {
      throw new Error('Invalid credentials');
    }
    
    // Get user data or create default user
    const user = mockUsers[username] || {
      id: Date.now(),
      username,
      role_code: 'USER',
      permissions: ['dashboard:view']
    };
    
    const token = generateMockToken(user);
    
    return {
      token,
      user
    };
  },

  async refreshToken(token: string): Promise<AuthResponse> {
    await delay(300);
    
    try {
      // Decode the mock token to get user info
      const payload = JSON.parse(atob(token.split('.')[1]));
      const user = mockUsers[payload.username] || {
        id: payload.user_id,
        username: payload.username,
        role_code: payload.role_code,
        permissions: payload.permissions
      };
      
      const newToken = generateMockToken(user);
      
      return {
        token: newToken,
        user
      };
    } catch (error) {
      throw new Error('Invalid token');
    }
  },

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  getCurrentUser(): UserInfo | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  getToken(): string | null {
    return localStorage.getItem('token');
  },

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
};