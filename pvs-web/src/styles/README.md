# Styles Directory Structure

This directory contains all the CSS styles for the application, organized in a modular and maintainable way.

## Directory Structure

```
styles/
├── index.css              # Main styles entry point
├── layout.css             # Global layout styles
└── components/            # Component-specific styles
    ├── dropdown.css       # Dropdown component styles
    ├── navbar.css         # Navbar component styles
    └── sidebar.css        # Sidebar component styles
```

## File Descriptions

### `index.css`
- Main entry point for all styles
- Imports all component styles
- Contains global utility classes and animations
- Imported in `src/index.tsx`

### `layout.css`
- Global layout styles
- Container and main content area styles
- Responsive layout adjustments

### `components/dropdown.css`
- Dropdown menu positioning and animations
- Hover effects for dropdown items
- Mobile responsive adjustments

### `components/navbar.css`
- Navigation bar styles
- User avatar and icon styling
- Chevron rotation animations

### `components/sidebar.css`
- Sidebar positioning and transitions
- Navigation link styles
- Collapse/expand animations
- Mobile responsive behavior

## Usage

All styles are automatically imported through the main `index.css` file, which is imported in `src/index.tsx`. Individual components no longer need to import their specific CSS files.

## Benefits of This Structure

1. **Modularity**: Each component has its own CSS file
2. **Maintainability**: Easy to find and modify component-specific styles
3. **Performance**: All styles are bundled together, reducing HTTP requests
4. **Consistency**: Global utilities and animations are centralized
5. **Scalability**: Easy to add new component styles following the same pattern

## Adding New Component Styles

1. Create a new CSS file in `components/` directory
2. Add the import to `index.css`
3. Use semantic class names that match the component

Example:
```css
/* components/button.css */
.custom-button {
  /* styles here */
}

.custom-button:hover {
  /* hover styles */
}
```

Then add to `index.css`:
```css
@import './components/button.css';
```
