/* Main Styles Index */

/* Import base layout styles */
@import './layout.css';

/* Import component styles */
@import './components/navbar.css';
@import './components/dropdown.css';
@import './components/sidebar.css';

/* Global utility classes */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-custom {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-down {
  animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive utilities */
@media (max-width: 576px) {
  .hide-on-mobile {
    display: none !important;
  }
}

@media (min-width: 577px) {
  .show-on-mobile-only {
    display: none !important;
  }
}
