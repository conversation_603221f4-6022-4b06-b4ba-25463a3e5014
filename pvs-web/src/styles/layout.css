/* Layout Styles */

/* Prevent horizontal scrollbar */
body {
  overflow-x: hidden;
}

/* Layout container */
.layout-container {
  display: flex;
}

/* Main content area */
.layout-main-content {
  flex-grow: 1;
  min-height: 100vh;
  transition: margin-left 0.3s ease-in-out;
  background-color: #f8f9fa;
  position: relative;
}

.layout-main-content.sidebar-collapsed {
  margin-left: 70px;
}

.layout-main-content.sidebar-expanded {
  margin-left: 280px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .layout-main-content.sidebar-collapsed,
  .layout-main-content.sidebar-expanded {
    margin-left: 0;
  }
}
