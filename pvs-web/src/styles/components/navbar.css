/* Navbar Component Styles */

.navbar {
  overflow: visible;
  position: relative;
  position: relative;
  z-index: 1040;
}

/* User avatar styling */
.user-avatar {
  width: 35px;
  height: 35px;
}

/* Chevron transition */
.chevron-icon {
  transition: transform 0.2s ease;
}

/* Chevron rotation animation */
.rotate-180 {
  transform: rotate(180deg);
}

/* Notification badge positioning */
.notification-badge {
  /* Additional styling for notification badge if needed */
}
