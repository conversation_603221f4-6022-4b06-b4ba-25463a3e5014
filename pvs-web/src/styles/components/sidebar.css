/* Sidebar Component Styles */

.sidebar {
  position: fixed;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 100;
  transition: width 0.3s ease-in-out;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%) !important;
}

.sidebar-expanded {
  width: 280px;
}

.sidebar-collapsed {
  width: 70px;
}

/* Sidebar header */
.sidebar-header {
  background: rgba(0, 0, 0, 0.1);
}

/* Sidebar navigation links */
.sidebar .nav-link {
  text-decoration: none;
  transition: all 0.3s ease-in-out;
}

.sidebar .nav-link:hover:not(.bg-primary) {
  background-color: rgba(255, 255, 255, 0.1) !important;
  transform: translateX(5px);
}

.sidebar .nav-link.bg-primary {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.sidebar .nav-link.active {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
  color: white !important;
}

/* Submenu button styling */
.sidebar button.nav-link {
  border: none;
  background: transparent;
  color: inherit;
  text-align: left;
}

.sidebar button.nav-link:focus {
  outline: none;
  box-shadow: none;
}

/* Submenu items */
.sidebar .nav-item .nav .nav-item .nav-link {
  padding-left: 2rem;
  font-size: 0.9rem;
  border-left: 2px solid rgba(255, 255, 255, 0.1);
  margin-left: 1rem;
}

.sidebar .nav-item .nav .nav-item .nav-link:hover:not(.bg-primary) {
  background-color: rgba(255, 255, 255, 0.08) !important;
  transform: translateX(3px);
  border-left-color: rgba(255, 255, 255, 0.3);
}

.sidebar .nav-item .nav .nav-item .nav-link.active {
  border-left-color: #007bff;
}

/* Sidebar icon styling */
.sidebar-icon {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Sidebar text */
.sidebar-text {
  transition: opacity 0.3s ease;
}

.sidebar-collapsed .sidebar-text {
  opacity: 0;
}

.sidebar-expanded .sidebar-text {
  opacity: 1;
}

/* Sidebar brand */
.sidebar-brand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 54px;
}

.sidebar-brand-text {
  transition: opacity 0.3s ease;
}

/* Sidebar toggle button */
.sidebar-toggle .btn {
  transition: all 0.2s ease;
}

.sidebar-toggle .btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Hover effects */
.hover-bg-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Transition utilities */
.transition-all {
  transition: all 0.3s ease-in-out;
}

/* User avatar */
.user-avatar {
  min-width: 40px;
  min-height: 40px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-expanded,
  .sidebar-collapsed {
    width: 280px;
  }
}
