/* Dropdown Component Styles */

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute !important;
  z-index: 1050;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.15);
  margin-top: 0.125rem;
  min-width: 200px;
  transform: translateY(0);
  transition: opacity 0.15s ease, transform 0.15s ease;
  opacity: 0;
  transform: translateY(-10px);
  pointer-events: none;
}

.dropdown-menu.show {
  display: block;
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Dropdown positioning for end-aligned dropdowns */
.dropdown-menu-end {
  right: 0 !important;
  left: auto !important;
}

/* Dropdown item hover effects */
.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.text-danger:hover {
  background-color: #f8d7da;
  color: #721c24 !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 180px;
  }
}
