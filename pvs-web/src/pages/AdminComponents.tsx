import React from 'react';
import { Layers, Plus, Search, Filter, Package, Cpu, HardDrive } from 'lucide-react';

const AdminComponentsPage: React.FC = () => {
  const components = [
    {
      id: 1,
      name: 'Component Database Core',
      type: 'Database',
      version: '2.1.5',
      status: 'active',
      description: 'Core database component for product management',
      dependencies: ['PostgreSQL 14', 'Redis 6.2'],
      lastUpdate: '2024-01-15'
    },
    {
      id: 2,
      name: 'Authentication Service',
      type: 'Service',
      version: '1.8.2',
      status: 'active',
      description: 'JWT-based authentication and authorization service',
      dependencies: ['JWT Library', 'bcrypt'],
      lastUpdate: '2024-01-12'
    },
    {
      id: 3,
      name: 'File Storage Handler',
      type: 'Storage',
      version: '3.0.1',
      status: 'maintenance',
      description: 'File upload and storage management component',
      dependencies: ['AWS S3', 'MinIO'],
      lastUpdate: '2024-01-10'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="badge bg-success">Hoạt động</span>;
      case 'maintenance':
        return <span className="badge bg-warning">Bảo trì</span>;
      case 'inactive':
        return <span className="badge bg-danger">Không hoạt động</span>;
      default:
        return <span className="badge bg-secondary">Không xác định</span>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Database':
        return <Package size={18} className="me-2 text-primary" />;
      case 'Service':
        return <Cpu size={18} className="me-2 text-success" />;
      case 'Storage':
        return <HardDrive size={18} className="me-2 text-warning" />;
      default:
        return <Layers size={18} className="me-2 text-secondary" />;
    }
  };

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Layers className="me-2" size={24} />
            Quản lý thành phần
          </h1>
          <p className="text-muted mb-0">Quản lý các thành phần và module của hệ thống</p>
        </div>
        <button className="btn btn-primary">
          <Plus size={18} className="me-2" />
          Thêm thành phần
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text">
                  <Search size={18} />
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm kiếm thành phần..."
                />
              </div>
            </div>
            <div className="col-md-3">
              <select className="form-select">
                <option value="">Tất cả loại</option>
                <option value="database">Database</option>
                <option value="service">Service</option>
                <option value="storage">Storage</option>
              </select>
            </div>
            <div className="col-md-3">
              <button className="btn btn-outline-secondary w-100">
                <Filter size={18} className="me-2" />
                Bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Components Grid */}
      <div className="row">
        {components.map((component) => (
          <div key={component.id} className="col-lg-4 col-md-6 mb-4">
            <div className="card h-100 shadow-sm">
              <div className="card-header d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  {getTypeIcon(component.type)}
                  <h6 className="mb-0">{component.name}</h6>
                </div>
                {getStatusBadge(component.status)}
              </div>
              <div className="card-body">
                <div className="mb-3">
                  <small className="text-muted">Loại:</small>
                  <div className="fw-bold">{component.type}</div>
                </div>
                <div className="mb-3">
                  <small className="text-muted">Phiên bản:</small>
                  <div className="fw-bold">{component.version}</div>
                </div>
                <div className="mb-3">
                  <small className="text-muted">Mô tả:</small>
                  <div className="text-sm">{component.description}</div>
                </div>
                <div className="mb-3">
                  <small className="text-muted">Dependencies:</small>
                  <div>
                    {component.dependencies.map((dep, index) => (
                      <span key={index} className="badge bg-light text-dark me-1 mb-1">
                        {dep}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="mb-3">
                  <small className="text-muted">Cập nhật cuối:</small>
                  <div className="text-sm">{component.lastUpdate}</div>
                </div>
              </div>
              <div className="card-footer">
                <div className="btn-group w-100" role="group">
                  <button className="btn btn-outline-primary btn-sm">Chi tiết</button>
                  <button className="btn btn-outline-secondary btn-sm">Cấu hình</button>
                  <button className="btn btn-outline-warning btn-sm">Logs</button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Component Statistics */}
      <div className="row mt-4">
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Package size={48} className="text-primary mb-3" />
              <h5 className="card-title">Database Components</h5>
              <h2 className="text-primary">8</h2>
              <p className="text-muted">6 hoạt động, 2 bảo trì</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Cpu size={48} className="text-success mb-3" />
              <h5 className="card-title">Services</h5>
              <h2 className="text-success">15</h2>
              <p className="text-muted">Tất cả đang hoạt động</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <HardDrive size={48} className="text-warning mb-3" />
              <h5 className="card-title">Storage Components</h5>
              <h2 className="text-warning">5</h2>
              <p className="text-muted">4 hoạt động, 1 bảo trì</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Layers size={48} className="text-info mb-3" />
              <h5 className="card-title">Tổng Components</h5>
              <h2 className="text-info">28</h2>
              <p className="text-muted">25 hoạt động, 3 bảo trì</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card mt-4">
        <div className="card-header">
          <h5 className="card-title mb-0">Hoạt động gần đây</h5>
        </div>
        <div className="card-body">
          <div className="list-group list-group-flush">
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Component Database Core updated</div>
                <small className="text-muted">Cập nhật từ phiên bản 2.1.4 lên 2.1.5</small>
              </div>
              <small className="text-muted">2 giờ trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">File Storage Handler maintenance</div>
                <small className="text-muted">Bắt đầu bảo trì định kỳ</small>
              </div>
              <small className="text-muted">4 giờ trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">New component deployed</div>
                <small className="text-muted">Email Service v1.0.0 đã được triển khai</small>
              </div>
              <small className="text-muted">1 ngày trước</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminComponentsPage;
