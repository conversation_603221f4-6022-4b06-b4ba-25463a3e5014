import React from 'react';
import { Sliders, Plus, Search, Filter, Settings, Globe, Database, Shield } from 'lucide-react';

const AdminParametersPage: React.FC = () => {
  const parameters = [
    {
      id: 1,
      key: 'MAX_FILE_SIZE',
      value: '10485760',
      displayValue: '10 MB',
      category: 'System',
      description: '<PERSON><PERSON><PERSON> thước tối đa của file upload',
      type: 'number',
      editable: true,
      lastModified: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      key: 'SESSION_TIMEOUT',
      value: '3600',
      displayValue: '1 giờ',
      category: 'Security',
      description: 'Thời gian timeout của phiên đăng nhập',
      type: 'number',
      editable: true,
      lastModified: '2024-01-12 14:20:00'
    },
    {
      id: 3,
      key: 'COMPANY_NAME',
      value: 'PVS Management System',
      displayValue: 'PVS Management System',
      category: 'General',
      description: '<PERSON><PERSON><PERSON> công ty hiển thị trên hệ thống',
      type: 'string',
      editable: true,
      lastModified: '2024-01-10 09:15:00'
    },
    {
      id: 4,
      key: 'DATABASE_BACKUP_INTERVAL',
      value: '86400',
      displayValue: '24 giờ',
      category: 'Database',
      description: 'Khoảng thời gian tự động backup database',
      type: 'number',
      editable: false,
      lastModified: '2024-01-08 16:45:00'
    }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'System':
        return <Settings size={18} className="me-2 text-primary" />;
      case 'Security':
        return <Shield size={18} className="me-2 text-danger" />;
      case 'General':
        return <Globe size={18} className="me-2 text-success" />;
      case 'Database':
        return <Database size={18} className="me-2 text-warning" />;
      default:
        return <Sliders size={18} className="me-2 text-secondary" />;
    }
  };

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'System':
        return <span className="badge bg-primary">{category}</span>;
      case 'Security':
        return <span className="badge bg-danger">{category}</span>;
      case 'General':
        return <span className="badge bg-success">{category}</span>;
      case 'Database':
        return <span className="badge bg-warning">{category}</span>;
      default:
        return <span className="badge bg-secondary">{category}</span>;
    }
  };

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Sliders className="me-2" size={24} />
            Quản lý tham số
          </h1>
          <p className="text-muted mb-0">Cấu hình các tham số và thiết lập hệ thống</p>
        </div>
        <button className="btn btn-primary">
          <Plus size={18} className="me-2" />
          Thêm tham số
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text">
                  <Search size={18} />
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm kiếm tham số..."
                />
              </div>
            </div>
            <div className="col-md-3">
              <select className="form-select">
                <option value="">Tất cả danh mục</option>
                <option value="system">System</option>
                <option value="security">Security</option>
                <option value="general">General</option>
                <option value="database">Database</option>
              </select>
            </div>
            <div className="col-md-3">
              <button className="btn btn-outline-secondary w-100">
                <Filter size={18} className="me-2" />
                Bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Parameters Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="card-title mb-0">Danh sách tham số</h5>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Tên tham số</th>
                  <th>Giá trị</th>
                  <th>Danh mục</th>
                  <th>Mô tả</th>
                  <th>Loại</th>
                  <th>Cập nhật cuối</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {parameters.map((param) => (
                  <tr key={param.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {getCategoryIcon(param.category)}
                        <code className="bg-light px-2 py-1 rounded">{param.key}</code>
                      </div>
                    </td>
                    <td>
                      <div>
                        <strong>{param.displayValue}</strong>
                        {param.displayValue !== param.value && (
                          <div>
                            <small className="text-muted">({param.value})</small>
                          </div>
                        )}
                      </div>
                    </td>
                    <td>{getCategoryBadge(param.category)}</td>
                    <td>
                      <small className="text-muted">{param.description}</small>
                    </td>
                    <td>
                      <span className="badge bg-light text-dark">{param.type}</span>
                    </td>
                    <td>
                      <small className="text-muted">{param.lastModified}</small>
                    </td>
                    <td>
                      <div className="btn-group" role="group">
                        {param.editable ? (
                          <>
                            <button className="btn btn-sm btn-outline-primary">Sửa</button>
                            <button className="btn btn-sm btn-outline-secondary">Sao chép</button>
                          </>
                        ) : (
                          <button className="btn btn-sm btn-outline-secondary" disabled>
                            Chỉ đọc
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Parameter Categories */}
      <div className="row mt-4">
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Settings size={48} className="text-primary mb-3" />
              <h5 className="card-title">System</h5>
              <h2 className="text-primary">15</h2>
              <p className="text-muted">Tham số hệ thống</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Shield size={48} className="text-danger mb-3" />
              <h5 className="card-title">Security</h5>
              <h2 className="text-danger">8</h2>
              <p className="text-muted">Tham số bảo mật</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Globe size={48} className="text-success mb-3" />
              <h5 className="card-title">General</h5>
              <h2 className="text-success">12</h2>
              <p className="text-muted">Tham số chung</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Database size={48} className="text-warning mb-3" />
              <h5 className="card-title">Database</h5>
              <h2 className="text-warning">6</h2>
              <p className="text-muted">Tham số cơ sở dữ liệu</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Changes */}
      <div className="card mt-4">
        <div className="card-header">
          <h5 className="card-title mb-0">Thay đổi gần đây</h5>
        </div>
        <div className="card-body">
          <div className="list-group list-group-flush">
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">MAX_FILE_SIZE updated</div>
                <small className="text-muted">Thay đổi từ 5MB thành 10MB</small>
              </div>
              <small className="text-muted">2 giờ trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">SESSION_TIMEOUT modified</div>
                <small className="text-muted">Tăng thời gian timeout từ 30 phút lên 1 giờ</small>
              </div>
              <small className="text-muted">1 ngày trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">New parameter added</div>
                <small className="text-muted">Thêm tham số EMAIL_NOTIFICATION_ENABLED</small>
              </div>
              <small className="text-muted">3 ngày trước</small>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration Backup */}
      <div className="card mt-4">
        <div className="card-header">
          <h5 className="card-title mb-0">Sao lưu cấu hình</h5>
        </div>
        <div className="card-body">
          <div className="row">
            <div className="col-md-6">
              <p className="text-muted">
                Tạo bản sao lưu của tất cả tham số cấu hình hiện tại để có thể khôi phục khi cần thiết.
              </p>
              <button className="btn btn-outline-primary me-2">
                <Database size={18} className="me-2" />
                Tạo backup
              </button>
              <button className="btn btn-outline-secondary">
                <Settings size={18} className="me-2" />
                Khôi phục
              </button>
            </div>
            <div className="col-md-6">
              <div className="list-group">
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <strong>Backup cuối cùng</strong>
                    <br />
                    <small className="text-muted">2024-01-15 08:00:00</small>
                  </div>
                  <button className="btn btn-sm btn-outline-primary">Tải xuống</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminParametersPage;
