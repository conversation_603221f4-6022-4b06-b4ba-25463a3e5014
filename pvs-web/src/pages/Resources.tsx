import React from 'react';
import { Database, Plus, Search, Filter, Server, HardDrive, Cpu } from 'lucide-react';

const ResourcesPage: React.FC = () => {
  const resources = [
    {
      id: 1,
      name: 'Server Database Chính',
      type: 'Database',
      status: 'active',
      usage: 75,
      location: 'Data Center A',
      lastUpdate: '2024-01-15 10:30:00'
    },
    {
      id: 2,
      name: 'Server Web Frontend',
      type: 'Web Server',
      status: 'active',
      usage: 45,
      location: 'Data Center B',
      lastUpdate: '2024-01-15 09:15:00'
    },
    {
      id: 3,
      name: 'Storage Backup',
      type: 'Storage',
      status: 'maintenance',
      usage: 90,
      location: 'Data Center A',
      lastUpdate: '2024-01-14 18:45:00'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="badge bg-success">Hoạt động</span>;
      case 'maintenance':
        return <span className="badge bg-warning">Bảo trì</span>;
      case 'inactive':
        return <span className="badge bg-danger">Không hoạt động</span>;
      default:
        return <span className="badge bg-secondary">Không xác đ<PERSON>nh</span>;
    }
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 80) return 'danger';
    if (usage >= 60) return 'warning';
    return 'success';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'Database':
        return <Database size={18} className="me-2" />;
      case 'Web Server':
        return <Server size={18} className="me-2" />;
      case 'Storage':
        return <HardDrive size={18} className="me-2" />;
      default:
        return <Cpu size={18} className="me-2" />;
    }
  };

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Database className="me-2" size={24} />
            Danh sách nguồn lực
          </h1>
          <p className="text-muted mb-0">Quản lý tài nguyên hệ thống và cơ sở hạ tầng</p>
        </div>
        <button className="btn btn-primary">
          <Plus size={18} className="me-2" />
          Thêm nguồn lực
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text">
                  <Search size={18} />
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm kiếm nguồn lực..."
                />
              </div>
            </div>
            <div className="col-md-3">
              <select className="form-select">
                <option value="">Tất cả loại</option>
                <option value="database">Database</option>
                <option value="server">Web Server</option>
                <option value="storage">Storage</option>
              </select>
            </div>
            <div className="col-md-3">
              <button className="btn btn-outline-secondary w-100">
                <Filter size={18} className="me-2" />
                Bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Resources Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="card-title mb-0">Danh sách nguồn lực</h5>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Tên nguồn lực</th>
                  <th>Loại</th>
                  <th>Trạng thái</th>
                  <th>Mức sử dụng</th>
                  <th>Vị trí</th>
                  <th>Cập nhật cuối</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {resources.map((resource) => (
                  <tr key={resource.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {getTypeIcon(resource.type)}
                        <strong>{resource.name}</strong>
                      </div>
                    </td>
                    <td>{resource.type}</td>
                    <td>{getStatusBadge(resource.status)}</td>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="progress me-2" style={{ width: '100px', height: '8px' }}>
                          <div
                            className={`progress-bar bg-${getUsageColor(resource.usage)}`}
                            role="progressbar"
                            style={{ width: `${resource.usage}%` }}
                          ></div>
                        </div>
                        <small>{resource.usage}%</small>
                      </div>
                    </td>
                    <td>{resource.location}</td>
                    <td>
                      <small className="text-muted">{resource.lastUpdate}</small>
                    </td>
                    <td>
                      <div className="btn-group" role="group">
                        <button className="btn btn-sm btn-outline-primary">Chi tiết</button>
                        <button className="btn btn-sm btn-outline-secondary">Sửa</button>
                        <button className="btn btn-sm btn-outline-danger">Xóa</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Resource Summary Cards */}
      <div className="row mt-4">
        <div className="col-md-4">
          <div className="card text-center">
            <div className="card-body">
              <Server size={48} className="text-primary mb-3" />
              <h5 className="card-title">Tổng số Server</h5>
              <h2 className="text-primary">12</h2>
              <p className="text-muted">8 hoạt động, 4 bảo trì</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-center">
            <div className="card-body">
              <Database size={48} className="text-success mb-3" />
              <h5 className="card-title">Database</h5>
              <h2 className="text-success">5</h2>
              <p className="text-muted">Tất cả đang hoạt động</p>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card text-center">
            <div className="card-body">
              <HardDrive size={48} className="text-warning mb-3" />
              <h5 className="card-title">Storage</h5>
              <h2 className="text-warning">8.5TB</h2>
              <p className="text-muted">Đã sử dụng 6.2TB</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourcesPage;
