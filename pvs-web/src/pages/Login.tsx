import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { authService } from '../services/authService';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await authService.login(formData.username, formData.password);
      localStorage.setItem('token', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const handleQuickLogin = (username: string) => {
    setFormData({ username, password: username });
  };

  return (
    <div className="container-fluid">
      <div className='mx-auto w-50'>
        <h1 className="text-center mt-5 mb-4">Login</h1>

        {/* Error Alert */}
        {error && (
          <div className="alert alert-danger border-0 rounded-3 mb-4" role="alert">
            <div className="d-flex align-items-center">
              <i className="fas fa-exclamation-triangle me-2"></i>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="username" className="form-label fw-semibold text-dark">
              <i className="fas fa-user me-2 text-muted"></i>Username
            </label>
            <input
              type="text"
              className="form-control form-control-lg border-0 rounded-3 shadow-sm"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleChange}
              required
              disabled={loading}
              placeholder="Enter your username"
              style={{
                backgroundColor: '#f8f9fa',
                transition: 'all 0.3s ease',
                fontSize: '1rem'
              }}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="password" className="form-label fw-semibold text-dark">
              <i className="fas fa-lock me-2 text-muted"></i>Password
            </label>
            <input
              type="password"
              className="form-control form-control-lg border-0 rounded-3 shadow-sm"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              disabled={loading}
              placeholder="Enter your password"
              style={{
                backgroundColor: '#f8f9fa',
                transition: 'all 0.3s ease',
                fontSize: '1rem'
              }}
            />
          </div>

          <button
            type="submit"
            className="btn btn-primary btn-lg w-100 rounded-3 fw-semibold mb-4 shadow-sm"
            disabled={loading}
            style={{
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              border: 'none',
              transition: 'all 0.3s ease',
              fontSize: '1.1rem',
              padding: '12px'
            }}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                Signing in...
              </>
            ) : (
              <>
                <i className="fas fa-sign-in-alt me-2"></i>
                Sign In
              </>
            )}
          </button>
        </form>

        {/* Divider */}
        <div className="text-center mb-4">
          <div className="d-flex align-items-center">
            <hr className="flex-grow-1" />
            <span className="px-3 text-muted small">Quick Access</span>
            <hr className="flex-grow-1" />
          </div>
        </div>

        {/* Quick Login Buttons */}
        <div className="row g-2">
          <div className="col-4">
            <button
              className="btn btn-outline-primary btn-sm w-100 rounded-3 fw-semibold"
              onClick={() => handleQuickLogin('admin')}
              disabled={loading}
              style={{
                transition: 'all 0.3s ease',
                fontSize: '0.85rem'
              }}
            >
              <i className="fas fa-crown me-1"></i>
              Admin
            </button>
          </div>
          <div className="col-4">
            <button
              className="btn btn-outline-success btn-sm w-100 rounded-3 fw-semibold"
              onClick={() => handleQuickLogin('user')}
              disabled={loading}
              style={{
                transition: 'all 0.3s ease',
                fontSize: '0.85rem'
              }}
            >
              <i className="fas fa-user me-1"></i>
              User
            </button>
          </div>
          <div className="col-4">
            <button
              className="btn btn-outline-info btn-sm w-100 rounded-3 fw-semibold"
              onClick={() => handleQuickLogin('viewer')}
              disabled={loading}
              style={{
                transition: 'all 0.3s ease',
                fontSize: '0.85rem'
              }}
            >
              <i className="fas fa-eye me-1"></i>
              Viewer
            </button>
          </div>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style> {`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        
        .form-control:focus {
          background-color: #ffffff !important;
          box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15) !important;
          border-color: #667eea !important;
        }
        
        .btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        
        .card {
          transition: all 0.3s ease;
        }
        
        .card:hover {
          transform: translateY(-5px);
        }
        
        .alert {
          animation: slideIn 0.5s ease-out;
        }
        
        @keyframes slideIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
      `}</style>
    </div>

  );
};

export default Login;
