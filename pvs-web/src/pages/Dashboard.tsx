import React from 'react';
import { authService } from '../services/authService';
import { Link } from 'react-router-dom';

const Dashboard: React.FC = () => {
  const user = authService.getCurrentUser();

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1 className="h3 mb-0">Dashboard</h1>
        <div className="text-muted">
          <i className="fas fa-calendar-alt me-2"></i>
          {new Date().toLocaleDateString()}
        </div>
      </div>
      
      {user && (
        <>
          {/* Stats Cards */}
          <div className="row mb-4">
            <div className="col-xl-3 col-md-6 mb-4">
              <div className="card border-0 shadow-sm h-100">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-grow-1">
                      <div className="text-xs fw-bold text-primary text-uppercase mb-1">
                        Total Users
                      </div>
                      <div className="h5 mb-0 fw-bold text-gray-800">40,000</div>
                    </div>
                    <div className="text-primary">
                      <i className="fas fa-users fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-xl-3 col-md-6 mb-4">
              <div className="card border-0 shadow-sm h-100">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-grow-1">
                      <div className="text-xs fw-bold text-success text-uppercase mb-1">
                        Revenue
                      </div>
                      <div className="h5 mb-0 fw-bold text-gray-800">$215,000</div>
                    </div>
                    <div className="text-success">
                      <i className="fas fa-dollar-sign fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-xl-3 col-md-6 mb-4">
              <div className="card border-0 shadow-sm h-100">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-grow-1">
                      <div className="text-xs fw-bold text-info text-uppercase mb-1">
                        Tasks
                      </div>
                      <div className="h5 mb-0 fw-bold text-gray-800">18</div>
                    </div>
                    <div className="text-info">
                      <i className="fas fa-clipboard-list fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="col-xl-3 col-md-6 mb-4">
              <div className="card border-0 shadow-sm h-100">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="flex-grow-1">
                      <div className="text-xs fw-bold text-warning text-uppercase mb-1">
                        Pending Requests
                      </div>
                      <div className="h5 mb-0 fw-bold text-gray-800">18</div>
                    </div>
                    <div className="text-warning">
                      <i className="fas fa-comments fa-2x"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="row">
            <div className="col-lg-8 mb-4">
              <div className="card border-0 shadow-sm">
                <div className="card-header bg-white py-3">
                  <h6 className="m-0 fw-bold text-primary">User Information</h6>
                </div>
                <div className="card-body">
                  <div className="row">
                    <div className="col-md-6">
                      <p><strong>Username:</strong> {user.username}</p>
                      <p><strong>Role:</strong> 
                        <span className={`badge ms-2 ${
                          user.role_code === 'ADMIN' ? 'bg-danger' : 
                          user.role_code === 'USER' ? 'bg-primary' : 'bg-secondary'
                        }`}>
                          {user.role_code}
                        </span>
                      </p>
                    </div>
                    <div className="col-md-6">
                      <p><strong>Permissions:</strong></p>
                      <div className="d-flex flex-wrap gap-1">
                        {user.permissions.map((permission, index) => (
                          <span key={index} className="badge bg-light text-dark border">
                            {permission}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="col-lg-4 mb-4">
              <div className="card border-0 shadow-sm">
                <div className="card-header bg-white py-3">
                  <h6 className="m-0 fw-bold text-primary">Quick Actions</h6>
                </div>
                <div className="card-body">
                  <div className="d-grid gap-2">
                    <Link to="/trello" className="btn btn-primary">
                      <i className="fas fa-columns me-2"></i>
                      Open Trello Board
                    </Link>
                    <button className="btn btn-outline-secondary">
                      <i className="fas fa-users me-2"></i>
                      Manage Users
                    </button>
                    <button className="btn btn-outline-info">
                      <i className="fas fa-chart-bar me-2"></i>
                      View Reports
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;
