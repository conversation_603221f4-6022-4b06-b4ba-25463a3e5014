import React from 'react';
import { Users, Plus, Search, Filter, UserCheck, UserX, Shield, Mail, Phone } from 'lucide-react';

const AdminUsersPage: React.FC = () => {
  const users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      fullName: 'Quản trị viên',
      role: 'Admin',
      status: 'active',
      lastLogin: '2024-01-15 10:30:00',
      phone: '+84 123 456 789',
      createdAt: '2023-01-01'
    },
    {
      id: 2,
      username: 'manager1',
      email: '<EMAIL>',
      fullName: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      role: 'Manager',
      status: 'active',
      lastLogin: '2024-01-15 09:15:00',
      phone: '+84 987 654 321',
      createdAt: '2023-02-15'
    },
    {
      id: 3,
      username: 'user1',
      email: '<EMAIL>',
      fullName: 'Trần Thị B',
      role: 'User',
      status: 'inactive',
      lastLogin: '2024-01-10 14:20:00',
      phone: '+84 555 666 777',
      createdAt: '2023-03-20'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="badge bg-success">Hoạt động</span>;
      case 'inactive':
        return <span className="badge bg-danger">Không hoạt động</span>;
      case 'suspended':
        return <span className="badge bg-warning">Tạm khóa</span>;
      default:
        return <span className="badge bg-secondary">Không xác định</span>;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'Admin':
        return <span className="badge bg-danger">Admin</span>;
      case 'Manager':
        return <span className="badge bg-warning">Manager</span>;
      case 'User':
        return <span className="badge bg-primary">User</span>;
      default:
        return <span className="badge bg-secondary">Unknown</span>;
    }
  };

  const getUserAvatar = (fullName: string) => {
    const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
    return (
      <div className="avatar-circle bg-primary text-white d-flex align-items-center justify-content-center">
        {initials}
      </div>
    );
  };

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Users className="me-2" size={24} />
            Quản lý người dùng
          </h1>
          <p className="text-muted mb-0">Quản lý tài khoản và quyền hạn người dùng</p>
        </div>
        <button className="btn btn-primary">
          <Plus size={18} className="me-2" />
          Thêm người dùng
        </button>
      </div>

      {/* Search and Filter Bar */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-6">
              <div className="input-group">
                <span className="input-group-text">
                  <Search size={18} />
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm kiếm người dùng..."
                />
              </div>
            </div>
            <div className="col-md-3">
              <select className="form-select">
                <option value="">Tất cả vai trò</option>
                <option value="admin">Admin</option>
                <option value="manager">Manager</option>
                <option value="user">User</option>
              </select>
            </div>
            <div className="col-md-3">
              <button className="btn btn-outline-secondary w-100">
                <Filter size={18} className="me-2" />
                Bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="card-title mb-0">Danh sách người dùng</h5>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Người dùng</th>
                  <th>Email</th>
                  <th>Số điện thoại</th>
                  <th>Vai trò</th>
                  <th>Trạng thái</th>
                  <th>Đăng nhập cuối</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        {getUserAvatar(user.fullName)}
                        <div className="ms-3">
                          <div className="fw-bold">{user.fullName}</div>
                          <small className="text-muted">@{user.username}</small>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="d-flex align-items-center">
                        <Mail size={16} className="me-2 text-muted" />
                        {user.email}
                      </div>
                    </td>
                    <td>
                      <div className="d-flex align-items-center">
                        <Phone size={16} className="me-2 text-muted" />
                        {user.phone}
                      </div>
                    </td>
                    <td>{getRoleBadge(user.role)}</td>
                    <td>{getStatusBadge(user.status)}</td>
                    <td>
                      <small className="text-muted">{user.lastLogin}</small>
                    </td>
                    <td>
                      <div className="btn-group" role="group">
                        <button className="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                          <UserCheck size={14} />
                        </button>
                        <button className="btn btn-sm btn-outline-warning" title="Phân quyền">
                          <Shield size={14} />
                        </button>
                        <button className="btn btn-sm btn-outline-danger" title="Khóa tài khoản">
                          <UserX size={14} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* User Statistics */}
      <div className="row mt-4">
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Shield size={48} className="text-danger mb-3" />
              <h5 className="card-title">Admins</h5>
              <h2 className="text-danger">3</h2>
              <p className="text-muted">Quản trị viên hệ thống</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <UserCheck size={48} className="text-warning mb-3" />
              <h5 className="card-title">Managers</h5>
              <h2 className="text-warning">12</h2>
              <p className="text-muted">Quản lý cấp trung</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <Users size={48} className="text-primary mb-3" />
              <h5 className="card-title">Users</h5>
              <h2 className="text-primary">156</h2>
              <p className="text-muted">Người dùng thường</p>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card text-center">
            <div className="card-body">
              <UserX size={48} className="text-secondary mb-3" />
              <h5 className="card-title">Inactive</h5>
              <h2 className="text-secondary">23</h2>
              <p className="text-muted">Tài khoản không hoạt động</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent User Activity */}
      <div className="card mt-4">
        <div className="card-header">
          <h5 className="card-title mb-0">Hoạt động gần đây</h5>
        </div>
        <div className="card-body">
          <div className="list-group list-group-flush">
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Người dùng mới đăng ký</div>
                <small className="text-muted">Lê Văn C đã tạo tài khoản mới</small>
              </div>
              <small className="text-muted">1 giờ trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Cập nhật quyền hạn</div>
                <small className="text-muted">Nguyễn Văn A được nâng cấp lên Manager</small>
              </div>
              <small className="text-muted">3 giờ trước</small>
            </div>
            <div className="list-group-item d-flex justify-content-between align-items-start">
              <div className="ms-2 me-auto">
                <div className="fw-bold">Tài khoản bị khóa</div>
                <small className="text-muted">Tài khoản user123 đã bị tạm khóa do vi phạm</small>
              </div>
              <small className="text-muted">1 ngày trước</small>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .avatar-circle {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          font-size: 14px;
          font-weight: bold;
        }
      `}</style>
    </div>
  );
};

export default AdminUsersPage;
