import React from 'react';
import { Clock, Calendar, Package, User } from 'lucide-react';

const ProductTimelinePage: React.FC = () => {
  const timelineEvents = [
    {
      id: 1,
      type: 'create',
      title: '<PERSON><PERSON><PERSON> sản phẩm mới',
      description: '<PERSON><PERSON><PERSON> phẩm "Laptop Gaming XYZ" đã được tạo',
      user: 'Nguyễn <PERSON>n <PERSON>',
      time: '2 giờ trước',
      icon: Package,
      color: 'success'
    },
    {
      id: 2,
      type: 'update',
      title: 'Cập nhật giá sản phẩm',
      description: '<PERSON><PERSON><PERSON> sản phẩm "Smartphone ABC" đã được cập nhật từ 10,000,000 VNĐ thành 9,500,000 VNĐ',
      user: 'Trần Thị B',
      time: '4 giờ trước',
      icon: Package,
      color: 'warning'
    },
    {
      id: 3,
      type: 'delete',
      title: '<PERSON><PERSON><PERSON> sản phẩm',
      description: '<PERSON><PERSON>n phẩm "Tablet DEF" đã bị xóa khỏi hệ thống',
      user: '<PERSON><PERSON> Văn C',
      time: '1 ngày trước',
      icon: Package,
      color: 'danger'
    }
  ];

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Clock className="me-2" size={24} />
            Dòng thời gian sản phẩm
          </h1>
          <p className="text-muted mb-0">Theo dõi các hoạt động liên quan đến sản phẩm</p>
        </div>
        <div className="d-flex gap-2">
          <select className="form-select" style={{ width: 'auto' }}>
            <option value="">Tất cả hoạt động</option>
            <option value="create">Tạo mới</option>
            <option value="update">Cập nhật</option>
            <option value="delete">Xóa</option>
          </select>
          <input
            type="date"
            className="form-control"
            style={{ width: 'auto' }}
          />
        </div>
      </div>

      {/* Timeline */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-header">
              <h5 className="card-title mb-0">
                <Calendar className="me-2" size={20} />
                Lịch sử hoạt động
              </h5>
            </div>
            <div className="card-body">
              <div className="timeline">
                {timelineEvents.map((event, index) => {
                  const IconComponent = event.icon;
                  return (
                    <div key={event.id} className="timeline-item">
                      <div className="timeline-marker">
                        <div className={`timeline-marker-icon bg-${event.color}`}>
                          <IconComponent size={16} className="text-white" />
                        </div>
                        {index < timelineEvents.length - 1 && (
                          <div className="timeline-line"></div>
                        )}
                      </div>
                      <div className="timeline-content">
                        <div className="card border-0 shadow-sm">
                          <div className="card-body">
                            <div className="d-flex justify-content-between align-items-start mb-2">
                              <h6 className="card-title mb-0">{event.title}</h6>
                              <small className="text-muted">{event.time}</small>
                            </div>
                            <p className="card-text text-muted mb-2">{event.description}</p>
                            <div className="d-flex align-items-center">
                              <User size={16} className="me-2 text-muted" />
                              <small className="text-muted">{event.user}</small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .timeline {
          position: relative;
          padding-left: 0;
        }

        .timeline-item {
          display: flex;
          margin-bottom: 2rem;
        }

        .timeline-marker {
          position: relative;
          margin-right: 1rem;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .timeline-marker-icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
        }

        .timeline-line {
          width: 2px;
          height: 60px;
          background-color: #e9ecef;
          margin-top: 0.5rem;
        }

        .timeline-content {
          flex: 1;
          margin-top: -5px;
        }
      `}</style>
    </div>
  );
};

export default ProductTimelinePage;
