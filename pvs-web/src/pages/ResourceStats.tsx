import React from 'react';
import { BarChart3, Activity, Zap, Clock, TrendingUp, AlertTriangle } from 'lucide-react';

const ResourceStatsPage: React.FC = () => {
  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <BarChart3 className="me-2" size={24} />
            Thống kê nguồn lực
          </h1>
          <p className="text-muted mb-0"><PERSON><PERSON> tích hiệu suất và sử dụng tài nguyên hệ thống</p>
        </div>
        <div className="d-flex gap-2">
          <select className="form-select" style={{ width: 'auto' }}>
            <option value="realtime">Thời gian thực</option>
            <option value="1h">1 giờ qua</option>
            <option value="24h">24 giờ qua</option>
            <option value="7d">7 ngày qua</option>
          </select>
          <button className="btn btn-outline-primary">
            <Activity size={18} className="me-2" />
            Làm mới
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-primary shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    CPU Usage
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">65.2%</div>
                  <div className="progress mt-2" style={{ height: '4px' }}>
                    <div className="progress-bar bg-primary" style={{ width: '65.2%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <Zap className="text-primary" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-success shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                    Memory Usage
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">78.5%</div>
                  <div className="progress mt-2" style={{ height: '4px' }}>
                    <div className="progress-bar bg-success" style={{ width: '78.5%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <Activity className="text-success" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-info shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Disk Usage
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">45.8%</div>
                  <div className="progress mt-2" style={{ height: '4px' }}>
                    <div className="progress-bar bg-info" style={{ width: '45.8%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <BarChart3 className="text-info" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-warning shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Network I/O
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">1.2 GB/s</div>
                  <div className="progress mt-2" style={{ height: '4px' }}>
                    <div className="progress-bar bg-warning" style={{ width: '60%' }}></div>
                  </div>
                </div>
                <div className="col-auto">
                  <TrendingUp className="text-warning" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Alerts */}
      <div className="row">
        {/* Performance Chart */}
        <div className="col-xl-8 col-lg-7">
          <div className="card shadow mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">
                <Activity className="me-2" size={20} />
                Biểu đồ hiệu suất hệ thống
              </h6>
            </div>
            <div className="card-body">
              <div className="chart-area">
                <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
                  <p className="text-muted">Biểu đồ hiệu suất sẽ được hiển thị ở đây</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* System Alerts */}
        <div className="col-xl-4 col-lg-5">
          <div className="card shadow mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-danger">
                <AlertTriangle className="me-2" size={20} />
                Cảnh báo hệ thống
              </h6>
            </div>
            <div className="card-body">
              <div className="list-group list-group-flush">
                <div className="list-group-item d-flex justify-content-between align-items-start">
                  <div className="ms-2 me-auto">
                    <div className="fw-bold text-warning">High Memory Usage</div>
                    <small>Server DB-01 đang sử dụng 89% RAM</small>
                  </div>
                  <span className="badge bg-warning rounded-pill">
                    <Clock size={12} />
                  </span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-start">
                  <div className="ms-2 me-auto">
                    <div className="fw-bold text-danger">Disk Space Low</div>
                    <small>Storage-02 chỉ còn 5% dung lượng trống</small>
                  </div>
                  <span className="badge bg-danger rounded-pill">
                    <AlertTriangle size={12} />
                  </span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-start">
                  <div className="ms-2 me-auto">
                    <div className="fw-bold text-info">Network Latency</div>
                    <small>Độ trễ mạng tăng cao trong 30 phút qua</small>
                  </div>
                  <span className="badge bg-info rounded-pill">
                    <TrendingUp size={12} />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resource Details Table */}
      <div className="card">
        <div className="card-header">
          <h5 className="card-title mb-0">Chi tiết sử dụng tài nguyên</h5>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  <th>Tài nguyên</th>
                  <th>CPU (%)</th>
                  <th>Memory (%)</th>
                  <th>Disk (%)</th>
                  <th>Network (MB/s)</th>
                  <th>Uptime</th>
                  <th>Trạng thái</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Server-DB-01</strong></td>
                  <td>
                    <span className="badge bg-warning">75%</span>
                  </td>
                  <td>
                    <span className="badge bg-danger">89%</span>
                  </td>
                  <td>
                    <span className="badge bg-success">45%</span>
                  </td>
                  <td>125.6</td>
                  <td>15 ngày 4 giờ</td>
                  <td><span className="badge bg-success">Online</span></td>
                </tr>
                <tr>
                  <td><strong>Server-Web-01</strong></td>
                  <td>
                    <span className="badge bg-success">45%</span>
                  </td>
                  <td>
                    <span className="badge bg-warning">67%</span>
                  </td>
                  <td>
                    <span className="badge bg-success">32%</span>
                  </td>
                  <td>89.3</td>
                  <td>8 ngày 12 giờ</td>
                  <td><span className="badge bg-success">Online</span></td>
                </tr>
                <tr>
                  <td><strong>Storage-02</strong></td>
                  <td>
                    <span className="badge bg-success">12%</span>
                  </td>
                  <td>
                    <span className="badge bg-success">34%</span>
                  </td>
                  <td>
                    <span className="badge bg-danger">95%</span>
                  </td>
                  <td>234.7</td>
                  <td>25 ngày 8 giờ</td>
                  <td><span className="badge bg-warning">Warning</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <style jsx>{`
        .border-left-primary {
          border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
          border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
          border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
          border-left: 0.25rem solid #f6c23e !important;
        }
      `}</style>
    </div>
  );
};

export default ResourceStatsPage;
