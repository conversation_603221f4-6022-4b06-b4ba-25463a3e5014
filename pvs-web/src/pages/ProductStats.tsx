import React from 'react';
import { TrendingUp, DollarSign, Package, ShoppingCart, BarChart3 } from 'lucide-react';

const ProductStatsPage: React.FC = () => {
  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <TrendingUp className="me-2" size={24} />
            Thống kê giao dịch
          </h1>
          <p className="text-muted mb-0">Phân tích doanh thu và giao dịch sản phẩm</p>
        </div>
        <div className="d-flex gap-2">
          <select className="form-select" style={{ width: 'auto' }}>
            <option value="7">7 ngày qua</option>
            <option value="30">30 ngày qua</option>
            <option value="90">3 tháng qua</option>
            <option value="365">1 năm qua</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-primary shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    Tổng doanh thu
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">
                    125,000,000 VNĐ
                  </div>
                </div>
                <div className="col-auto">
                  <DollarSign className="text-primary" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-success shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                    Số giao dịch
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">1,234</div>
                </div>
                <div className="col-auto">
                  <ShoppingCart className="text-success" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-info shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Sản phẩm bán chạy
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">89</div>
                </div>
                <div className="col-auto">
                  <Package className="text-info" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-xl-3 col-md-6 mb-4">
          <div className="card border-left-warning shadow h-100 py-2">
            <div className="card-body">
              <div className="row no-gutters align-items-center">
                <div className="col mr-2">
                  <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Tăng trưởng
                  </div>
                  <div className="h5 mb-0 font-weight-bold text-gray-800">+15.2%</div>
                </div>
                <div className="col-auto">
                  <TrendingUp className="text-warning" size={32} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="row">
        {/* Revenue Chart */}
        <div className="col-xl-8 col-lg-7">
          <div className="card shadow mb-4">
            <div className="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 className="m-0 font-weight-bold text-primary">
                <BarChart3 className="me-2" size={20} />
                Biểu đồ doanh thu theo tháng
              </h6>
            </div>
            <div className="card-body">
              <div className="chart-area">
                <div className="d-flex justify-content-center align-items-center" style={{ height: '300px' }}>
                  <p className="text-muted">Biểu đồ doanh thu sẽ được hiển thị ở đây</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="col-xl-4 col-lg-5">
          <div className="card shadow mb-4">
            <div className="card-header py-3">
              <h6 className="m-0 font-weight-bold text-primary">
                <Package className="me-2" size={20} />
                Top sản phẩm bán chạy
              </h6>
            </div>
            <div className="card-body">
              <div className="list-group list-group-flush">
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Laptop Gaming XYZ</h6>
                    <small className="text-muted">234 giao dịch</small>
                  </div>
                  <span className="badge bg-primary rounded-pill">1</span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Smartphone ABC</h6>
                    <small className="text-muted">189 giao dịch</small>
                  </div>
                  <span className="badge bg-primary rounded-pill">2</span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Tablet DEF</h6>
                    <small className="text-muted">156 giao dịch</small>
                  </div>
                  <span className="badge bg-primary rounded-pill">3</span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Headphone GHI</h6>
                    <small className="text-muted">123 giao dịch</small>
                  </div>
                  <span className="badge bg-primary rounded-pill">4</span>
                </div>
                <div className="list-group-item d-flex justify-content-between align-items-center">
                  <div>
                    <h6 className="mb-1">Mouse JKL</h6>
                    <small className="text-muted">98 giao dịch</small>
                  </div>
                  <span className="badge bg-primary rounded-pill">5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .border-left-primary {
          border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
          border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
          border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
          border-left: 0.25rem solid #f6c23e !important;
        }
      `}</style>
    </div>
  );
};

export default ProductStatsPage;
