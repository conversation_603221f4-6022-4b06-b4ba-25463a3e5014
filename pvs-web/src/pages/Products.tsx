import React, { useState, useEffect } from 'react';
import { Package, ChevronLeft, ChevronRight, ChevronDown, Star } from 'lucide-react';
import { productService, type GroupedProductsResponse, type ProductFilters } from '../services/productService';

const ProductsPage: React.FC = () => {
  const [data, setData] = useState<GroupedProductsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productGroups, setProductGroups] = useState<{ id: number; name: string; description: string }[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set());

  // Filter states
  const [filters, setFilters] = useState<ProductFilters>({
    page: 1,
    limit: 20,
    group_id: undefined,
    year: undefined,
    status: '',
    group_is_favorite: undefined,
  });

  // Load product groups for filter dropdown
  useEffect(() => {
    const loadProductGroups = async () => {
      try {
        const groups = await productService.getProductGroups();
        setProductGroups(groups);
      } catch (err) {
        console.error('Failed to load product groups:', err);
      }
    };
    loadProductGroups();
  }, []);

  // Load grouped products
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await productService.getGroupedProducts(filters);
        setData(result);
        // Expand all groups that have products by default
        const groupsWithProducts = new Set(
          result.groups
            .filter(group => group.products.length > 0)
            .map(group => group.id)
        );
        setExpandedGroups(groupsWithProducts);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products');
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value, // Reset to page 1 when changing filters
    }));
  };

  // Handle group expand/collapse
  const toggleGroupExpansion = (groupId: number) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  // Handle group favorite toggle
  const handleToggleGroupFavorite = async (groupId: number, currentFavorite: boolean) => {
    try {
      await productService.toggleProductGroupFavorite(groupId, !currentFavorite);
      // Reload data to reflect changes
      const result = await productService.getGroupedProducts(filters);
      setData(result);
      // Update expanded groups to maintain state
      const groupsWithProducts = new Set(
        result.groups
          .filter(group => group.products.length > 0)
          .map(group => group.id)
      );
      setExpandedGroups(groupsWithProducts);
    } catch (err) {
      console.error('Failed to toggle group favorite:', err);
      alert('Failed to update group favorite status');
    }
  };



  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-success';
      case 'inactive': return 'bg-secondary';
      case 'idle': return 'bg-warning';
      default: return 'bg-secondary';
    }
  };

  // Generate year options (from 2006 to current year)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: currentYear - 2006 + 1 }, (_, i) => currentYear - i);

  return (
    <div className="container-fluid py-2">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800">
            <Package className="me-2" size={24} />
            Danh sách sản phẩm
          </h1>
          <p className="text-muted mb-0">Quản lý tất cả sản phẩm và dịch vụ</p>
        </div>
      </div>

      {/* Filters Bar */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            <div className="col-md-3">
              <div className="form-floating">
                <select
                  className="form-select"
                  id="groupFilter"
                  value={filters.group_id || ''}
                  onChange={(e) => handleFilterChange('group_id', e.target.value ? parseInt(e.target.value) : undefined)}
                >
                  <option value="">Tất cả nhóm</option>
                  {productGroups.map(group => (
                    <option key={group.id} value={group.id}>{group.name}</option>
                  ))}
                </select>
                <label htmlFor="groupFilter">Nhóm sản phẩm</label>
              </div>
            </div>
            <div className="col-md-3">
              <div className="form-floating">
                <select
                  className="form-select"
                  id="yearFilter"
                  value={filters.year || ''}
                  onChange={(e) => handleFilterChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
                >
                  <option value="">Tất cả năm</option>
                  {yearOptions.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
                <label htmlFor="yearFilter">Năm</label>
              </div>
            </div>
            <div className="col-md-3">
              <div className="form-floating">
                <select
                  className="form-select"
                  id="statusFilter"
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <option value="">Tất cả trạng thái</option>
                  <option value="ACTIVE">Hoạt động</option>
                  <option value="INACTIVE">Không hoạt động</option>
                  <option value="IDLE">Duy trì</option>
                </select>
                <label htmlFor="statusFilter">Trạng thái</label>
              </div>
            </div>
            <div className="col-md-3">
              <div className="form-floating">
                <select
                  className="form-select"
                  id="groupFavoriteFilter"
                  value={filters.group_is_favorite === undefined ? '' : filters.group_is_favorite.toString()}
                  onChange={(e) => handleFilterChange('group_is_favorite', e.target.value === '' ? undefined : e.target.value === 'true')}
                >
                  <option value="">Tất cả nhóm</option>
                  <option value="true">Nhóm yêu thích</option>
                  <option value="false">Nhóm bình thường</option>
                </select>
                <label htmlFor="groupFavoriteFilter">Nhóm yêu thích</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="card">
          <div className="card-body text-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3 mb-0">Đang tải dữ liệu...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="card">
          <div className="card-body">
            <div className="alert alert-danger" role="alert">
              <strong>Lỗi:</strong> {error}
            </div>
          </div>
        </div>
      )}

      {/* Products Content */}
      {!loading && !error && data && (
        <>


          {/* No data message */}
          {data.groups.filter(group => group.products.length > 0).length === 0 && (
            <div className="card">
              <div className="card-body text-center py-5">
                <div className="text-muted">
                  <Package size={48} className="mb-3 opacity-50" />
                  <h5>Không tìm thấy sản phẩm</h5>
                  <p className="mb-0">Không có sản phẩm nào phù hợp với bộ lọc hiện tại.</p>
                </div>
              </div>
            </div>
          )}

          {/* Tree View - Groups and Products */}
          {data.groups.filter(group => group.products.length > 0).length > 0 && (
            <div className="card">
              <div className="card-body p-0">
                <div className="table-responsive">
                  <table className="table table-hover mb-0">
                    <thead className="table-light">
                      <tr>
                        <th style={{ width: '40px' }}></th>
                        <th>Tên</th>
                        <th>Mô tả</th>
                        <th>Trạng thái</th>
                        <th style={{ width: '120px' }}>Yêu thích</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.groups
                        .filter(group => group.products.length > 0)
                        .map(group => (
                          <React.Fragment key={group.id}>
                            {/* Group Row */}
                            <tr className="table-secondary">
                              <td>
                                <button
                                  className="btn btn-sm btn-link p-0"
                                  onClick={() => toggleGroupExpansion(group.id)}
                                  title={expandedGroups.has(group.id) ? 'Thu gọn' : 'Mở rộng'}
                                >
                                  {expandedGroups.has(group.id) ? (
                                    <ChevronDown size={16} />
                                  ) : (
                                    <ChevronRight size={16} />
                                  )}
                                </button>
                              </td>
                              <td>
                                <strong className="text-primary">
                                  📁 {group.name} ({group.products.length} sản phẩm)
                                </strong>
                              </td>
                              <td colSpan={2}></td>
                              <td>
                                <button
                                  className="btn btn-sm p-0"
                                  style={{
                                    background: 'transparent',
                                    border: 'none',
                                    color: group.is_favorite ? '#ffc107' : '#ffc107'
                                  }}
                                  onClick={() => handleToggleGroupFavorite(group.id, group.is_favorite)}
                                  title={group.is_favorite ? 'Bỏ yêu thích nhóm' : 'Đánh dấu yêu thích nhóm'}
                                >
                                  <Star
                                    size={20}
                                    fill={group.is_favorite ? '#ffc107' : 'none'}
                                    stroke="#ffc107"
                                    strokeWidth={2}
                                  />
                                </button>
                              </td>
                            </tr>

                            {/* Products Rows */}
                            {expandedGroups.has(group.id) && group.products.map(product => (
                              <tr key={product.id}>
                                <td>
                                  <div className="ms-3">
                                    <div className="border-start border-2 border-light ps-2">
                                      📄
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  <div className="ms-3">
                                    <strong>{product.name}</strong>
                                  </div>
                                </td>
                                <td>
                                  <span className="text-muted">
                                    {product.description || 'Không có mô tả'}
                                  </span>
                                </td>
                                <td>
                                  <span className={`badge ${getStatusBadgeClass(product.status)}`}>
                                    {product.status}
                                  </span>
                                </td>
                                <td>
                                  {/* Cột trống cho yêu thích - chỉ group mới có switch */}
                                </td>
                              </tr>
                            ))}
                          </React.Fragment>
                        ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Pagination */}
          <div className="card mt-4">
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-center">
                <div className="text-muted">
                  <div>Hiển thị {data.count} sản phẩm trong trang này, tổng cộng {data.total} sản phẩm</div>
                  <div className="mt-1">
                    Trang {filters.page || 1} / {Math.ceil(data.total / (filters.limit || 20))}
                  </div>
                </div>

                <nav aria-label="Products pagination">
                  <ul className="pagination mb-0">
                    {/* First page */}
                    <li className={`page-item ${(filters.page || 1) <= 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handleFilterChange('page', 1)}
                        disabled={(filters.page || 1) <= 1}
                        title="Trang đầu"
                      >
                        «
                      </button>
                    </li>

                    {/* Previous page */}
                    <li className={`page-item ${(filters.page || 1) <= 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handleFilterChange('page', (filters.page || 1) - 1)}
                        disabled={(filters.page || 1) <= 1}
                        title="Trang trước"
                      >
                        <ChevronLeft size={16} />
                      </button>
                    </li>

                    {/* Page numbers */}
                    {Array.from({ length: Math.ceil(data.total / (filters.limit || 20)) }, (_, i) => i + 1)
                      .filter(page => {
                        const currentPage = filters.page || 1;
                        const totalPages = Math.ceil(data.total / (filters.limit || 20));
                        return page === 1 || page === totalPages || Math.abs(page - currentPage) <= 2;
                      })
                      .map((page, index, array) => {
                        const showEllipsis = index > 0 && array[index - 1] < page - 1;
                        return (
                          <React.Fragment key={page}>
                            {showEllipsis && (
                              <li className="page-item disabled">
                                <span className="page-link">...</span>
                              </li>
                            )}
                            <li className={`page-item ${(filters.page || 1) === page ? 'active' : ''}`}>
                              <button
                                className="page-link"
                                onClick={() => handleFilterChange('page', page)}
                              >
                                {page}
                              </button>
                            </li>
                          </React.Fragment>
                        );
                      })}

                    {/* Next page */}
                    <li className={`page-item ${(filters.page || 1) >= Math.ceil(data.total / (filters.limit || 20)) ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handleFilterChange('page', (filters.page || 1) + 1)}
                        disabled={(filters.page || 1) >= Math.ceil(data.total / (filters.limit || 20))}
                        title="Trang sau"
                      >
                        <ChevronRight size={16} />
                      </button>
                    </li>

                    {/* Last page */}
                    <li className={`page-item ${(filters.page || 1) >= Math.ceil(data.total / (filters.limit || 20)) ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => handleFilterChange('page', Math.ceil(data.total / (filters.limit || 20)))}
                        disabled={(filters.page || 1) >= Math.ceil(data.total / (filters.limit || 20))}
                        title="Trang cuối"
                      >
                        »
                      </button>
                    </li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ProductsPage;
