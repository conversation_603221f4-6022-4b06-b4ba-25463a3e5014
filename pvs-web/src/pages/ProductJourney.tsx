import React, { useState, useEffect } from 'react';
import { Route, Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import {
  productJourneyService,
  type ProductJourneyListItem,
  type ProductJourneyFilters,
  type PaginatedResponse,
  type ProductJourneyRequest
} from '../services/productJourneyService';
import { productService } from '../services/productService';
import ProductJourneyForm from '../components/ProductJourneyForm';

const ProductJourneyPage: React.FC = () => {
  const [data, setData] = useState<PaginatedResponse<ProductJourneyListItem> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<{ id: number; name: string; description: string }[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Filter states
  const [filters, setFilters] = useState<ProductJourneyFilters>({
    page: 1,
    limit: 20,
    product_id: undefined,
    status: '',
    search: '',
  });

  // Load product journeys
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await productJourneyService.getProductJourneys(filters);
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load product journeys');
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [filters]);

  // Load products for filter dropdown
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const result = await productService.getProductGroups();
        setProducts(result);
      } catch (err) {
        console.error('Failed to load products:', err);
      }
    };
    loadProducts();
  }, []);

  const handleFilterChange = (key: keyof ProductJourneyFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa hành trình này?')) {
      return;
    }

    try {
      await productJourneyService.deleteProductJourney(id);
      // Reload data
      const result = await productJourneyService.getProductJourneys(filters);
      setData(result);
    } catch (err) {
      alert('Không thể xóa hành trình: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleCreateJourney = async (journeyData: ProductJourneyRequest) => {
    try {
      await productJourneyService.createProductJourney(journeyData);
      setShowCreateModal(false);
      // Reload data
      const result = await productJourneyService.getProductJourneys(filters);
      setData(result);
    } catch (err) {
      throw err; // Let the form handle the error
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-success';
      case 'INACTIVE':
        return 'bg-secondary';
      case 'DRAFT':
        return 'bg-warning';
      default:
        return 'bg-primary';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert alert-danger" role="alert">
        <h4 className="alert-heading">Lỗi!</h4>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-gray-800 d-flex align-items-center">
            <Route className="me-2" size={28} />
            Hành trình sản phẩm
          </h1>
          <p className="text-muted mb-0">Quản lý hành trình khách hàng cho các sản phẩm</p>
        </div>
        <button
          className="btn btn-primary d-flex align-items-center"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus size={18} className="me-2" />
          Thêm hành trình mới
        </button>
      </div>

      {/* Filters */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="row g-3">
            {/* Search */}
            <div className="col-md-4">
              <label className="form-label">Tìm kiếm</label>
              <div className="input-group">
                <span className="input-group-text">
                  <Search size={16} />
                </span>
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm theo tên hoặc mô tả..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>
            </div>

            {/* Product Filter */}
            <div className="col-md-3">
              <label className="form-label">Sản phẩm</label>
              <select
                className="form-select"
                value={filters.product_id || ''}
                onChange={(e) => handleFilterChange('product_id', e.target.value ? parseInt(e.target.value) : undefined)}
              >
                <option value="">Tất cả sản phẩm</option>
                {products.map(product => (
                  <option key={product.id} value={product.id}>
                    {product.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div className="col-md-3">
              <label className="form-label">Trạng thái</label>
              <select
                className="form-select"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="">Tất cả trạng thái</option>
                <option value="ACTIVE">Hoạt động</option>
                <option value="INACTIVE">Không hoạt động</option>
                <option value="DRAFT">Bản nháp</option>
              </select>
            </div>

            {/* Clear Filters */}
            <div className="col-md-2 d-flex align-items-end">
              <button
                className="btn btn-outline-secondary w-100"
                onClick={() => setFilters({ page: 1, limit: 20 })}
              >
                <Filter size={16} className="me-1" />
                Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      {data && (
        <>
          {/* Summary */}
          <div className="row mb-3">
            <div className="col">
              <p className="text-muted mb-0">
                Hiển thị {data.data.length} trong tổng số {data.pagination.total} hành trình
              </p>
            </div>
          </div>

          {/* Table */}
          {data.data.length > 0 ? (
            <div className="card">
              <div className="card-body p-0">
                <div className="table-responsive">
                  <table className="table table-hover mb-0">
                    <thead className="table-light">
                      <tr>
                        <th>Tên hành trình</th>
                        <th>Sản phẩm</th>
                        <th>Mô tả</th>
                        <th>Components</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th style={{ width: '120px' }}>Thao tác</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.data.map(journey => (
                        <tr key={journey.id}>
                          <td>
                            <strong>{journey.name}</strong>
                          </td>
                          <td>
                            <span className="text-primary">{journey.product_name}</span>
                          </td>
                          <td>
                            <span className="text-muted">
                              {journey.description || 'Không có mô tả'}
                            </span>
                          </td>
                          <td>
                            <span className="badge bg-secondary">{journey.component_count} components</span>
                          </td>
                          <td>
                            <span className={`badge ${getStatusBadgeClass(journey.status)}`}>
                              {journey.status}
                            </span>
                          </td>
                          <td>
                            {new Date(journey.created_at).toLocaleDateString('vi-VN')}
                          </td>
                          <td>
                            <div className="btn-group btn-group-sm">
                              <button
                                className="btn btn-outline-primary"
                                title="Xem chi tiết"
                              >
                                <Eye size={14} />
                              </button>
                              <button
                                className="btn btn-outline-warning"
                                title="Chỉnh sửa"
                              >
                                <Edit size={14} />
                              </button>
                              <button
                                className="btn btn-outline-danger"
                                title="Xóa"
                                onClick={() => handleDelete(journey.id)}
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          ) : (
            <div className="card">
              <div className="card-body text-center py-5">
                <Route size={48} className="text-muted mb-3" />
                <h5 className="text-muted">Không có hành trình nào</h5>
                <p className="text-muted">Chưa có hành trình nào được tạo cho các sản phẩm.</p>
                <button
                  className="btn btn-primary"
                  onClick={() => setShowCreateModal(true)}
                >
                  <Plus size={18} className="me-2" />
                  Tạo hành trình đầu tiên
                </button>
              </div>
            </div>
          )}

          {/* Pagination */}
          {data.pagination.total_pages > 1 && (
            <nav className="mt-4">
              <ul className="pagination justify-content-center">
                <li className={`page-item ${data.pagination.page === 1 ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(data.pagination.page - 1)}
                    disabled={data.pagination.page === 1}
                  >
                    Trước
                  </button>
                </li>

                {Array.from({ length: data.pagination.total_pages }, (_, i) => i + 1).map(page => (
                  <li key={page} className={`page-item ${page === data.pagination.page ? 'active' : ''}`}>
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </button>
                  </li>
                ))}

                <li className={`page-item ${data.pagination.page === data.pagination.total_pages ? 'disabled' : ''}`}>
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(data.pagination.page + 1)}
                    disabled={data.pagination.page === data.pagination.total_pages}
                  >
                    Sau
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </>
      )}

      {/* Create Modal */}
      {showCreateModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-xl">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Thêm hành trình mới</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowCreateModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <ProductJourneyForm
                  onSubmit={handleCreateJourney}
                  onCancel={() => setShowCreateModal(false)}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductJourneyPage;
