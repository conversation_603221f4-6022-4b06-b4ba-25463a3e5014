import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Columns,
  Users,
  FolderKanban,
  BarChart3,
  Settings,
  Shield,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path: string;
  badge?: string;
  badgeColor?: string;
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard'
  },
  {
    id: 'trello',
    title: 'Trello Board',
    icon: Columns,
    path: '/trello',
    badge: 'New',
    badgeColor: 'success'
  },
  {
    id: 'users',
    title: 'Users',
    icon: Users,
    path: '/users'
  },
  {
    id: 'projects',
    title: 'Projects',
    icon: Folder<PERSON><PERSON>ban,
    path: '/projects'
  },
  {
    id: 'reports',
    title: 'Reports',
    icon: BarChart3,
    path: '/reports'
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: Settings,
    path: '/settings'
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const location = useLocation();

  const isActiveRoute = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div
      className={`sidebar bg-dark text-white d-flex flex-column shadow-lg ${
        isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
      }`}
    >
      {/* Header */}
      <div className="sidebar-header p-2 border-bottom border-secondary">
        <div className="d-flex align-items-center">
          <div className="sidebar-brand-icon">
            <Shield size={24} className="text-primary" />
          </div>
          <div className={`sidebar-brand-text ms-3 ${isCollapsed ? 'd-none' : ''}`}>
            <h5 className="mb-0 fw-bold">PVS Admin</h5>
            <small className="text-white">Management System</small>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav flex-grow-1 py-3">
        <ul className="nav flex-column">
          {menuItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <li key={item.id} className="nav-item mb-1">
                <Link
                  to={item.path}
                  className={`nav-link d-flex align-items-center px-3 py-2 mx-2 rounded ${
                    isActiveRoute(item.path)
                      ? 'bg-primary text-white shadow-sm active'
                      : 'text-light hover-bg-secondary'
                  }`}
                  title={isCollapsed ? item.title : ''}
                >
                  <div className="sidebar-icon">
                    <IconComponent size={18} />
                  </div>
                  <span className={`sidebar-text ms-3 flex-grow-1 ${isCollapsed ? 'd-none' : ''}`}>
                    {item.title}
                  </span>
                  {item.badge && !isCollapsed && (
                    <span className={`badge bg-${item.badgeColor || 'primary'} ms-2`}>
                      {item.badge}
                    </span>
                  )}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Toggle Button */}
      <div className="sidebar-toggle p-3 border-top border-secondary">
        <button
          className="btn btn-outline-light w-100 d-flex align-items-center justify-content-center"
          onClick={onToggle}
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          <span className={`ms-2 ${isCollapsed ? 'd-none' : ''}`}>Collapse Menu</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
