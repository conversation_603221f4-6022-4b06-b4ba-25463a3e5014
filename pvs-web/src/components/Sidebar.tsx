import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Package,
  List,
  Clock,
  TrendingUp,
  Database,
  BarChart3,
  Settings,
  Shield,
  ArrowLeftFromLine,
  ArrowRightFromLine,
  ChevronDown,
  ChevronUp,
  Users,
  Layers,
  Sliders,
  Route
} from 'lucide-react';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path?: string;
  badge?: string;
  badgeColor?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard'
  },
  {
    id: 'products-services',
    title: 'Sản phẩm dịch vụ',
    icon: Package,
    children: [
      {
        id: 'products-list',
        title: '<PERSON><PERSON> sách sản phẩm',
        icon: List,
        path: '/products'
      },
      {
        id: 'products-journey',
        title: '<PERSON>ành trình sản phẩm',
        icon: Route,
        path: '/products/journey'
      },
      {
        id: 'products-timeline',
        title: 'Dòng thời gian',
        icon: Clock,
        path: '/products/timeline'
      },
      {
        id: 'products-stats',
        title: 'Thống kê giao dịch',
        icon: TrendingUp,
        path: '/products/stats'
      }
    ]
  },
  {
    id: 'resources',
    title: 'Nguồn lực',
    icon: Database,
    children: [
      {
        id: 'resources-list',
        title: 'Danh sách',
        icon: List,
        path: '/resources'
      },
      {
        id: 'resources-stats',
        title: 'Thống kê',
        icon: BarChart3,
        path: '/resources/stats'
      }
    ]
  },
  {
    id: 'admin',
    title: 'Quản trị',
    icon: Settings,
    children: [
      {
        id: 'admin-components',
        title: 'Thành phần',
        icon: Layers,
        path: '/admin/components'
      },
      {
        id: 'admin-users',
        title: 'Người dùng',
        icon: Users,
        path: '/admin/users'
      },
      {
        id: 'admin-parameters',
        title: 'Tham số',
        icon: Sliders,
        path: '/admin/parameters'
      }
    ]
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  const isActiveRoute = (path?: string) => {
    if (!path) return false;
    return location.pathname === path;
  };

  const isMenuActive = (item: MenuItem): boolean => {
    if (item.path && isActiveRoute(item.path)) return true;
    if (item.children) {
      return item.children.some(child => isActiveRoute(child.path));
    }
    return false;
  };

  const toggleSubmenu = (menuId: string) => {
    if (isCollapsed) return; // Don't toggle submenus when sidebar is collapsed

    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  return (
    <div
      className={`sidebar bg-dark text-white d-flex flex-column shadow-lg ${isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
        }`}
    >
      {/* Header */}
      <div className="sidebar-header p-2 border-bottom border-secondary">
        <div className="d-flex align-items-center">
          <div className="sidebar-brand-icon">
            <Shield size={24} className="text-primary" />
          </div>
          <div className={`sidebar-brand-text ms-3 ${isCollapsed ? 'd-none' : ''}`}>
            <h5 className="mb-0 fw-bold">PVS Admin</h5>
            <small className="text-white">Management System</small>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav flex-grow-1 py-3">
        <ul className="nav flex-column">
          {menuItems.map((item) => {
            const IconComponent = item.icon;
            const hasChildren = item.children && item.children.length > 0;
            const isExpanded = expandedMenus.has(item.id);
            const isActive = isMenuActive(item);

            return (
              <li key={item.id} className="nav-item mb-1">
                {/* Main menu item */}
                {hasChildren ? (
                  <button
                    className={`nav-link d-flex align-items-center px-3 py-2 rounded border-0 bg-transparent w-100 text-start ${isActive
                      ? 'bg-primary text-white shadow-sm active'
                      : 'text-light hover-bg-secondary'
                      }`}
                    onClick={() => toggleSubmenu(item.id)}
                    title={isCollapsed ? item.title : ''}
                  >
                    <div className="sidebar-icon">
                      <IconComponent size={18} />
                    </div>
                    <span className={`sidebar-text ms-3 flex-grow-1 ${isCollapsed ? 'd-none' : ''}`}>
                      {item.title}
                    </span>
                    {item.badge && !isCollapsed && (
                      <span className={`badge bg-${item.badgeColor || 'primary'} ms-2`}>
                        {item.badge}
                      </span>
                    )}
                    {!isCollapsed && (
                      <div className="ms-2">
                        {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                      </div>
                    )}
                  </button>
                ) : (
                  <Link
                    to={item.path || '#'}
                    className={`nav-link d-flex align-items-center px-3 py-2 rounded ${isActiveRoute(item.path)
                      ? 'bg-primary text-white shadow-sm active'
                      : 'text-light hover-bg-secondary'
                      }`}
                    title={isCollapsed ? item.title : ''}
                  >
                    <div className="sidebar-icon">
                      <IconComponent size={18} />
                    </div>
                    <span className={`sidebar-text ms-3 flex-grow-1 ${isCollapsed ? 'd-none' : ''}`}>
                      {item.title}
                    </span>
                    {item.badge && !isCollapsed && (
                      <span className={`badge bg-${item.badgeColor || 'primary'} ms-2`}>
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}

                {/* Submenu items */}
                {hasChildren && !isCollapsed && isExpanded && (
                  <ul className="nav flex-column ms-3 mt-1">
                    {item.children!.map((child) => {
                      const ChildIconComponent = child.icon;
                      return (
                        <li key={child.id} className="nav-item">
                          <Link
                            to={child.path || '#'}
                            className={`nav-link d-flex align-items-center px-3 py-1 rounded ${isActiveRoute(child.path)
                              ? 'bg-primary text-white shadow-sm active'
                              : 'text-light hover-bg-secondary'
                              }`}
                            style={{ fontSize: '0.9rem' }}
                          >
                            <div className="sidebar-icon">
                              <ChildIconComponent size={16} />
                            </div>
                            <span className="sidebar-text ms-3">
                              {child.title}
                            </span>
                            {child.badge && (
                              <span className={`badge bg-${child.badgeColor || 'primary'} ms-2`}>
                                {child.badge}
                              </span>
                            )}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                )}
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Toggle Button */}
      <div className="sidebar-toggle p-3 border-top border-secondary">
        <button
          className="btn btn-outline-light d-flex align-items-center justify-content-center"
          onClick={onToggle}
          title={isCollapsed ? "Expand Menu" : "Collapse Menu"}
          style={{ width: '40px', height: '40px', marginLeft: 'auto' }}
        >
          {isCollapsed ? <ArrowRightFromLine size={18} /> : <ArrowLeftFromLine size={18} />}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
