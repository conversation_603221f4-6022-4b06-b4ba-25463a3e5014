import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Columns,
  Users,
  FolderKanban,
  BarChart3,
  Settings,
  Shield,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  UserPlus,
  UserCheck,
  FileText,
  TrendingUp,
  PieChart,
  Calendar,
  Cog,
  Key
} from 'lucide-react';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  path?: string;
  badge?: string;
  badgeColor?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: LayoutDashboard,
    path: '/dashboard'
  },
  {
    id: 'trello',
    title: 'Trello Board',
    icon: Columns,
    path: '/trello',
    badge: 'New',
    badgeColor: 'success'
  },
  {
    id: 'users',
    title: 'Users',
    icon: Users,
    children: [
      {
        id: 'users-list',
        title: 'All Users',
        icon: Users,
        path: '/users'
      },
      {
        id: 'users-add',
        title: 'Add User',
        icon: UserPlus,
        path: '/users/add'
      },
      {
        id: 'users-roles',
        title: 'User Roles',
        icon: UserCheck,
        path: '/users/roles'
      }
    ]
  },
  {
    id: 'projects',
    title: 'Projects',
    icon: FolderKanban,
    path: '/projects'
  },
  {
    id: 'reports',
    title: 'Reports',
    icon: BarChart3,
    children: [
      {
        id: 'reports-analytics',
        title: 'Analytics',
        icon: TrendingUp,
        path: '/reports/analytics'
      },
      {
        id: 'reports-charts',
        title: 'Charts',
        icon: PieChart,
        path: '/reports/charts'
      },
      {
        id: 'reports-schedule',
        title: 'Scheduled Reports',
        icon: Calendar,
        path: '/reports/schedule'
      }
    ]
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: Settings,
    children: [
      {
        id: 'settings-general',
        title: 'General',
        icon: Cog,
        path: '/settings/general'
      },
      {
        id: 'settings-security',
        title: 'Security',
        icon: Key,
        path: '/settings/security'
      }
    ]
  }
];

const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState<Set<string>>(new Set());

  const isActiveRoute = (path?: string) => {
    if (!path) return false;
    return location.pathname === path;
  };

  const isMenuActive = (item: MenuItem): boolean => {
    if (item.path && isActiveRoute(item.path)) return true;
    if (item.children) {
      return item.children.some(child => isActiveRoute(child.path));
    }
    return false;
  };

  const toggleSubmenu = (menuId: string) => {
    if (isCollapsed) return; // Don't toggle submenus when sidebar is collapsed

    setExpandedMenus(prev => {
      const newSet = new Set(prev);
      if (newSet.has(menuId)) {
        newSet.delete(menuId);
      } else {
        newSet.add(menuId);
      }
      return newSet;
    });
  };

  return (
    <div
      className={`sidebar bg-dark text-white d-flex flex-column shadow-lg ${
        isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
      }`}
    >
      {/* Header */}
      <div className="sidebar-header p-2 border-bottom border-secondary">
        <div className="d-flex align-items-center">
          <div className="sidebar-brand-icon">
            <Shield size={24} className="text-primary" />
          </div>
          <div className={`sidebar-brand-text ms-3 ${isCollapsed ? 'd-none' : ''}`}>
            <h5 className="mb-0 fw-bold">PVS Admin</h5>
            <small className="text-white">Management System</small>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav flex-grow-1 py-3">
        <ul className="nav flex-column">
          {menuItems.map((item) => {
            const IconComponent = item.icon;
            const hasChildren = item.children && item.children.length > 0;
            const isExpanded = expandedMenus.has(item.id);
            const isActive = isMenuActive(item);

            return (
              <li key={item.id} className="nav-item mb-1">
                {/* Main menu item */}
                {hasChildren ? (
                  <button
                    className={`nav-link d-flex align-items-center px-3 py-2 mx-2 rounded border-0 bg-transparent w-100 text-start ${
                      isActive
                        ? 'bg-primary text-white shadow-sm active'
                        : 'text-light hover-bg-secondary'
                    }`}
                    onClick={() => toggleSubmenu(item.id)}
                    title={isCollapsed ? item.title : ''}
                  >
                    <div className="sidebar-icon">
                      <IconComponent size={18} />
                    </div>
                    <span className={`sidebar-text ms-3 flex-grow-1 ${isCollapsed ? 'd-none' : ''}`}>
                      {item.title}
                    </span>
                    {item.badge && !isCollapsed && (
                      <span className={`badge bg-${item.badgeColor || 'primary'} ms-2`}>
                        {item.badge}
                      </span>
                    )}
                    {!isCollapsed && (
                      <div className="ms-2">
                        {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                      </div>
                    )}
                  </button>
                ) : (
                  <Link
                    to={item.path || '#'}
                    className={`nav-link d-flex align-items-center px-3 py-2 mx-2 rounded ${
                      isActiveRoute(item.path)
                        ? 'bg-primary text-white shadow-sm active'
                        : 'text-light hover-bg-secondary'
                    }`}
                    title={isCollapsed ? item.title : ''}
                  >
                    <div className="sidebar-icon">
                      <IconComponent size={18} />
                    </div>
                    <span className={`sidebar-text ms-3 flex-grow-1 ${isCollapsed ? 'd-none' : ''}`}>
                      {item.title}
                    </span>
                    {item.badge && !isCollapsed && (
                      <span className={`badge bg-${item.badgeColor || 'primary'} ms-2`}>
                        {item.badge}
                      </span>
                    )}
                  </Link>
                )}

                {/* Submenu items */}
                {hasChildren && !isCollapsed && isExpanded && (
                  <ul className="nav flex-column ms-3 mt-1">
                    {item.children!.map((child) => {
                      const ChildIconComponent = child.icon;
                      return (
                        <li key={child.id} className="nav-item">
                          <Link
                            to={child.path || '#'}
                            className={`nav-link d-flex align-items-center px-3 py-1 mx-2 rounded ${
                              isActiveRoute(child.path)
                                ? 'bg-primary text-white shadow-sm active'
                                : 'text-light hover-bg-secondary'
                            }`}
                            style={{ fontSize: '0.9rem' }}
                          >
                            <div className="sidebar-icon">
                              <ChildIconComponent size={16} />
                            </div>
                            <span className="sidebar-text ms-3">
                              {child.title}
                            </span>
                            {child.badge && (
                              <span className={`badge bg-${child.badgeColor || 'primary'} ms-2`}>
                                {child.badge}
                              </span>
                            )}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                )}
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Toggle Button */}
      <div className="sidebar-toggle p-3 border-top border-secondary">
        <button
          className="btn btn-outline-light d-flex align-items-center justify-content-center"
          onClick={onToggle}
          title={isCollapsed ? "Expand Menu" : "Collapse Menu"}
          style={{ width: '40px', height: '40px', marginLeft: 'auto' }}
        >
          {isCollapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
