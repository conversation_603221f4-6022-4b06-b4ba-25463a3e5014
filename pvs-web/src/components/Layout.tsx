import React, { useState, useRef, useEffect } from 'react';
import Sidebar from './Sidebar';
import { authService } from '../services/authService';
import { useNavigate } from 'react-router-dom';
import {
  Menu,
  Bell,
  User,
  ChevronDown,
  Info,
  AlertTriangle,
  CheckCircle,
  Settings,
  LogOut
} from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const navigate = useNavigate();
  const user = authService.getCurrentUser();

  const notificationDropdownRef = useRef<HTMLDivElement>(null);
  const userDropdownRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside dropdowns to close them
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false);
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSidebarToggle = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  const toggleNotificationDropdown = () => {
    setNotificationDropdownOpen(!notificationDropdownOpen);
    setUserDropdownOpen(false); // Close other dropdown
  };

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!userDropdownOpen);
    setNotificationDropdownOpen(false); // Close other dropdown
  };

  return (
    <div className="layout-container">
      <Sidebar isCollapsed={sidebarCollapsed} onToggle={handleSidebarToggle} />

      <div
        className={`layout-main-content ${sidebarCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'}`}
      >
        {/* Top Navigation Bar */}
        <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm border-bottom">
          <div className="container-fluid">
            <div className="d-flex align-items-center">
              <button
                className="btn btn-link d-md-none me-2"
                onClick={handleSidebarToggle}
              >
                <Menu size={20} />
              </button>
              <h4 className="mb-0 text-dark">Welcome back, {user?.username}!</h4>
            </div>

            <div className="d-flex align-items-center">
              {/* Notifications */}
              <div className="dropdown me-3" ref={notificationDropdownRef}>
                <button
                  className="btn btn-link text-dark position-relative"
                  type="button"
                  onClick={toggleNotificationDropdown}
                >
                  <Bell size={20} />
                  <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    3
                  </span>
                </button>
                <ul className={`dropdown-menu dropdown-menu-end ${notificationDropdownOpen ? 'show' : ''}`}>
                  <li><h6 className="dropdown-header">Notifications</h6></li>
                  <li><a className="dropdown-item" href="#"><Info size={16} className="text-info me-2" />New user registered</a></li>
                  <li><a className="dropdown-item" href="#"><AlertTriangle size={16} className="text-warning me-2" />Server maintenance</a></li>
                  <li><a className="dropdown-item" href="#"><CheckCircle size={16} className="text-success me-2" />Backup completed</a></li>
                </ul>
              </div>

              {/* User Menu */}
              <div className="dropdown" ref={userDropdownRef}>
                <button
                  className="btn btn-link text-dark d-flex align-items-center"
                  type="button"
                  onClick={toggleUserDropdown}
                >
                  <div className="user-avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                    <User size={18} className="text-white" />
                  </div>
                  <span className="d-none d-sm-inline">{user?.username}</span>
                  <ChevronDown size={16} className={`ms-2 chevron-icon ${userDropdownOpen ? 'rotate-180' : ''}`} />
                </button>
                <ul className={`dropdown-menu dropdown-menu-end ${userDropdownOpen ? 'show' : ''}`}>
                  <li><h6 className="dropdown-header">Account</h6></li>
                  <li><a className="dropdown-item" href="#"><User size={16} className="me-2" />Profile</a></li>
                  <li><a className="dropdown-item" href="#"><Settings size={16} className="me-2" />Settings</a></li>
                  <li><hr className="dropdown-divider" /></li>
                  <li>
                    <button className="dropdown-item text-danger" onClick={handleLogout}>
                      <LogOut size={16} className="me-2" />Logout
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </nav>

        {/* Page Content */}
        <main className="p-2">
          {children}
        </main>
      </div>
    </div>
  );
};

export default Layout;