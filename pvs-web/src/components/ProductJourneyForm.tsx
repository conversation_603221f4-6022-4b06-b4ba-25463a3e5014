import React, { useState, useEffect } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import {
  type ProductJourney,
  type ProductJourneyRequest,
  type ProductJourneyComponent
} from '../services/productJourneyService';
import { productService } from '../services/productService';
import { componentService, type DropdownItem } from '../services/componentService';

interface ComponentOption {
  id: number;
  name: string;
}

interface ProductJourneyFormProps {
  journey?: ProductJourney;
  onSubmit: (data: ProductJourneyRequest) => Promise<void>;
  onCancel: () => void;
}

const ProductJourneyForm: React.FC<ProductJourneyFormProps> = ({ journey, onSubmit, onCancel }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<{ id: number; name: string }[]>([]);
  const [components, setComponents] = useState<ComponentOption[]>([]);

  // Form state
  const [formData, setFormData] = useState<ProductJourneyRequest>({
    name: journey?.name || '',
    description: journey?.description || '',
    product_id: journey?.product_id || 0,
    flow_data: journey?.flow_data || JSON.stringify({
      components: {},
      actions: {},
      steps: [
        { id: "step1", title: "Nhận diện", actionIds: [] },
        { id: "step2", title: "Cân nhắc", actionIds: [] },
        { id: "step3", title: "Mua hàng", actionIds: [] },
        { id: "step4", title: "Sử dụng", actionIds: [] },
        { id: "step5", title: "Giữ chân", actionIds: [] },
      ]
    }),
    components: journey?.components?.map(c => ({
      component_id: c.component_id
    })) || [],
    status: journey?.status || 'ACTIVE'
  });

  // Load products and components
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load products
        const productsData = await productService.getProductGroups();
        setProducts(productsData);

        // Load components from API
        const componentsData = await componentService.getComponentsForDropdown();
        setComponents(componentsData);

        // If editing and no product selected, set the first product
        if (!journey && productsData.length > 0 && formData.product_id === 0) {
          setFormData(prev => ({ ...prev, product_id: productsData[0].id }));
        }
      } catch (err) {
        setError('Failed to load form data');
        console.error(err);
      }
    };

    loadData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAddComponent = () => {
    if (components.length === 0) return;

    const newComponent: ProductJourneyComponent = {
      component_id: components[0].id
    };

    setFormData(prev => ({
      ...prev,
      components: [...prev.components, newComponent]
    }));
  };

  const handleRemoveComponent = (index: number) => {
    setFormData(prev => ({
      ...prev,
      components: prev.components.filter((_, i) => i !== index)
    }));
  };

  const handleComponentChange = (index: number, field: keyof ProductJourneyComponent, value: any) => {
    setFormData(prev => {
      const updatedComponents = [...prev.components];
      updatedComponents[index] = {
        ...updatedComponents[index],
        [field]: field === 'component_id' ? parseInt(value) : value
      };
      return { ...prev, components: updatedComponents };
    });
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await onSubmit(formData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save journey');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}

      <div className="row mb-3">
        <div className="col-md-6">
          <label htmlFor="name" className="form-label">Tên hành trình <span className="text-danger">*</span></label>
          <input
            type="text"
            className="form-control"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            required
          />
        </div>
        <div className="col-md-6">
          <label htmlFor="product_id" className="form-label">Sản phẩm <span className="text-danger">*</span></label>
          <select
            className="form-select"
            id="product_id"
            name="product_id"
            value={formData.product_id}
            onChange={handleInputChange}
            required
          >
            <option value="">Chọn sản phẩm</option>
            {products.map(product => (
              <option key={product.id} value={product.id}>
                {product.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="mb-3">
        <label htmlFor="description" className="form-label">Mô tả</label>
        <textarea
          className="form-control"
          id="description"
          name="description"
          rows={3}
          value={formData.description}
          onChange={handleInputChange}
        />
      </div>

      <div className="mb-3">
        <label htmlFor="status" className="form-label">Trạng thái</label>
        <select
          className="form-select"
          id="status"
          name="status"
          value={formData.status}
          onChange={handleInputChange}
        >
          <option value="ACTIVE">Hoạt động</option>
          <option value="INACTIVE">Không hoạt động</option>
          <option value="DRAFT">Bản nháp</option>
        </select>
      </div>

      {/* Components Section */}
      <div className="card mb-4">
        <div className="card-header d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Components tham gia</h5>
          <button
            type="button"
            className="btn btn-sm btn-outline-primary"
            onClick={handleAddComponent}
          >
            <Plus size={16} className="me-1" />
            Thêm component
          </button>
        </div>
        <div className="card-body">
          {formData.components.length === 0 ? (
            <p className="text-muted text-center py-3">Chưa có component nào được thêm vào hành trình</p>
          ) : (
            <div className="table-responsive">
              <table className="table table-bordered">
                <thead>
                  <tr>
                    <th style={{ width: '80%' }}>Component</th>
                    <th style={{ width: '20%' }}>Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.components.map((component, index) => (
                    <tr key={index}>
                      <td>
                        <select
                          className="form-select"
                          value={component.component_id}
                          onChange={(e) => handleComponentChange(index, 'component_id', e.target.value)}
                        >
                          {components.map(comp => (
                            <option key={comp.id} value={comp.id}>
                              {comp.name}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="text-center">
                        <button
                          type="button"
                          className="btn btn-sm btn-outline-danger"
                          onClick={() => handleRemoveComponent(index)}
                        >
                          <Trash2 size={14} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>



      {/* Flow Data JSON */}
      <div className="mb-4">
        <label htmlFor="flow_data" className="form-label">Dữ liệu luồng (JSON)</label>
        <textarea
          className="form-control font-monospace"
          id="flow_data"
          name="flow_data"
          rows={5}
          value={formData.flow_data}
          onChange={handleInputChange}
          placeholder='{"steps": [], "connections": []}'
        />
        <div className="form-text">
          Dữ liệu JSON mô tả luồng hành trình, có thể sử dụng để hiển thị diagram.
        </div>
      </div>

      <div className="d-flex justify-content-end gap-2">
        <button
          type="button"
          className="btn btn-secondary"
          onClick={onCancel}
          disabled={loading}
        >
          Hủy
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={loading}
        >
          {loading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Đang lưu...
            </>
          ) : journey ? 'Cập nhật hành trình' : 'Tạo hành trình'}
        </button>
      </div>
    </form>
  );
};

export default ProductJourneyForm;
