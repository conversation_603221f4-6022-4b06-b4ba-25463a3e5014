import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import TrelloBoardPage from './pages/TrelloBoard';
import ProjectPage from './pages/Projects';
import ProductsPage from './pages/Products';
import ProductTimelinePage from './pages/ProductTimeline';
import ProductStatsPage from './pages/ProductStats';
import ProductJourneyPage from './pages/ProductJourney';
import ResourcesPage from './pages/Resources';
import ResourceStatsPage from './pages/ResourceStats';
import AdminComponentsPage from './pages/AdminComponents';
import AdminUsersPage from './pages/AdminUsers';
import AdminParametersPage from './pages/AdminParameters';
import Layout from './components/Layout';
import { authService } from './services/authService';

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return authService.isAuthenticated() ? <>{children}</> : <Navigate to="/login" />;
};

const App: React.FC = () => {
  return (
    <Router>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Layout>
                <Dashboard />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/trello"
          element={
            <ProtectedRoute>
              <Layout>
                <TrelloBoardPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/projects"
          element={
            <ProtectedRoute>
              <Layout>
                <ProjectPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Products and Services Routes */}
        <Route
          path="/products"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/journey"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductJourneyPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/timeline"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductTimelinePage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/products/stats"
          element={
            <ProtectedRoute>
              <Layout>
                <ProductStatsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Resources Routes */}
        <Route
          path="/resources"
          element={
            <ProtectedRoute>
              <Layout>
                <ResourcesPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/resources/stats"
          element={
            <ProtectedRoute>
              <Layout>
                <ResourceStatsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        {/* Admin Routes */}
        <Route
          path="/admin/components"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminComponentsPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/users"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminUsersPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route
          path="/admin/parameters"
          element={
            <ProtectedRoute>
              <Layout>
                <AdminParametersPage />
              </Layout>
            </ProtectedRoute>
          }
        />
        <Route path="/" element={<Navigate to="/dashboard" />} />
      </Routes>
    </Router>
  );
};

export default App;
