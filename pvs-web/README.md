# Etuto Admin Dashboard

A modern, responsive admin dashboard for the Etuto learning management system built with React, TypeScript, and Bootstrap.

## Features

- 🎨 **Modern UI Design** - Clean and professional interface
- 📱 **Responsive Layout** - Works on desktop, tablet, and mobile
- 🔐 **Authentication** - Secure login with JWT tokens
- 📊 **Dashboard** - Overview of key metrics and recent activity
- 🎯 **Collapsible Sidebar** - Space-efficient navigation
- 🚀 **Fast Development** - Hot module replacement with Vite
- 📝 **TypeScript** - Type-safe development
- 🎭 **Form Validation** - React Hook Form with Yup validation

## Tech Stack

- **React 19** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **React Router** - Client-side routing
- **Bootstrap 5** - CSS framework
- **React Bootstrap** - Bootstrap components for React
- **React Hook Form** - Form handling
- **Yup** - Schema validation
- **Axios** - HTTP client
- **Lucide React** - Modern icons

## Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm or yarn

### Installation

1. **Install dependencies**
   ```bash
   pnpm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your API URL:
   ```
   VITE_API_URL=http://localhost:8080/api
   ```

3. **Start the development server**
   ```bash
   pnpm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Default Login Credentials

For development/demo purposes:
- **Email:** <EMAIL>
- **Password:** password

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── DashboardLayout.tsx
│   ├── Header.tsx
│   ├── ProtectedRoute.tsx
│   └── Sidebar.tsx
├── contexts/           # React contexts
│   └── AuthContext.tsx
├── pages/              # Page components
│   ├── Dashboard.tsx
│   ├── Login.tsx
│   ├── Courses.tsx
│   └── Students.tsx
├── services/           # API services
│   ├── authService.ts
│   └── dashboardService.ts
├── types/              # TypeScript type definitions
│   └── index.ts
├── App.tsx             # Main app component
├── main.tsx            # App entry point
└── index.css           # Global styles
```

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run preview` - Preview production build
- `pnpm run lint` - Run ESLint

## Features Overview

### Dashboard Layout
- **Collapsible Sidebar** - Toggle between expanded and collapsed states
- **Mobile Responsive** - Sidebar transforms to overlay on mobile devices
- **Active Navigation** - Highlights current page in sidebar
- **User Menu** - Profile dropdown with logout functionality

### Authentication
- **JWT Token Management** - Automatic token storage and refresh
- **Protected Routes** - Redirects to login if not authenticated
- **Form Validation** - Real-time validation with error messages
- **Auto-redirect** - Redirects to dashboard after successful login

### Dashboard
- **Statistics Cards** - Key metrics overview
- **Recent Activity** - Latest courses and users
- **Responsive Grid** - Adapts to different screen sizes

## Customization

### Styling
The app uses CSS custom properties for easy theming. Update the variables in `src/index.css`:

```css
:root {
  --primary-color: #6366f1;
  --sidebar-bg: #1f2937;
  --sidebar-text: #d1d5db;
  /* ... other variables */
}
```

### Adding New Pages
1. Create a new component in `src/pages/`
2. Add the route to `src/App.tsx`
3. Add navigation item to `src/components/Sidebar.tsx`

### API Integration
Update the service files in `src/services/` to integrate with your backend API.

## Deployment

1. **Build the project**
   ```bash
   pnpm run build
   ```

2. **Deploy the `dist` folder** to your hosting provider

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is part of the Etuto learning management system.
